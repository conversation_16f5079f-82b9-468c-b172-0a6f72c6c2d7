"""
Stage2 Pipeline Integration Helpers
Provides helper functions to integrate comprehensive pipeline execution tracking
with existing Stage2 execution methods
"""

import os
from config import Config
from Conversion_Agent_Stage2.utils.stage2_pipeline_execution_tracker import Stage2PipelineExecutionTracker
from Conversion_Agent_Stage2.state import Stage2WorkflowState


def get_stage2_pipeline_logs_directory(state: Stage2WorkflowState) -> str:
    """
    Get Stage2 pipeline execution logs directory using QBook path structure.

    Args:
        state: Stage2 workflow state

    Returns:
        Pipeline execution logs directory path
    """
    if state.cloud_category.lower() == "local":
        base_path = Config.Qbook_Local_Path
    else:
        base_path = Config.Qbook_Path

    return os.path.join(
        base_path,
        "Stage1_Metadata",
        state.migration_name,
        state.schema_name,
        state.objecttype,  # e.g., "Procedure"
        state.object_name,
        "pipeline_logs"
    )


def determine_stage2_execution_context(state: Stage2WorkflowState) -> str:
    """
    Determine execution context for developer insight.
    
    Args:
        state: Stage2 workflow state
        
    Returns:
        Execution context description
    """
    execution_source = getattr(state, 'execution_source', 'enhancement')
    enhanced_modules_loaded = getattr(state, 'enhanced_modules_loaded', False)
    registry_status = getattr(state, 'registry_status', 'none_in_registry')
    current_attempt = getattr(state, 'current_attempt', 1)
    
    # Handle all execution source scenarios from Stage2 workflow
    if execution_source == 'registry':
        return "Enhanced Direct Pipeline - Registry Execution (All modules from registry - 90% faster)"
    elif execution_source == 'mixed':
        return "Enhanced Direct Pipeline - Mixed Execution (Enhanced + Original modules - 50-80% faster)"
    elif execution_source == 'enhancement':
        if current_attempt == 1:
            return "Enhancement Pipeline - Initial Enhancement (AI-driven module enhancement)"
        else:
            return f"Enhancement Pipeline - Retry Enhancement (Attempt {current_attempt} with feedback)"
    elif execution_source == 'registry_failed':
        return "Enhancement Pipeline - Registry Failed Transition (Registry execution failed, transitioning to enhancement)"
    elif execution_source == 'mixed_failed':
        return "Enhancement Pipeline - Mixed Failed Transition (Mixed execution failed, transitioning to enhancement)"
    else:
        # Fallback based on registry status for edge cases
        if registry_status == 'all_in_registry' and enhanced_modules_loaded:
            return "Enhanced Direct Pipeline - Registry Available (All modules in registry)"
        elif registry_status == 'some_in_registry' and enhanced_modules_loaded:
            return "Enhanced Direct Pipeline - Mixed Available (Some modules in registry)"
        elif registry_status == 'none_in_registry' or not enhanced_modules_loaded:
            return "Enhancement Pipeline - Full Enhancement (No enhanced modules available)"
        else:
            return f"Standard Pipeline - Unknown Context (execution_source: {execution_source})"


def create_stage2_pipeline_execution_tracker(state: Stage2WorkflowState) -> Stage2PipelineExecutionTracker:
    """
    Create Stage2 pipeline execution tracker for current statement/attempt.

    Args:
        state: Stage2 workflow state

    Returns:
        Configured Stage2PipelineExecutionTracker instance
    """
    # Get current statement information
    current_statement_index = getattr(state, 'current_statement_index', 0)
    available_features_with_statements = getattr(state, 'available_features_with_statements', [])

    # Get target statement number from current statement data
    if current_statement_index < len(available_features_with_statements):
        current_statement = available_features_with_statements[current_statement_index]
        target_statement_number = current_statement.get('target_statement_number', current_statement_index + 1)
    else:
        target_statement_number = current_statement_index + 1

    # Determine execution context
    execution_context = determine_stage2_execution_context(state)

    # Create pipeline execution tracker
    tracker = Stage2PipelineExecutionTracker(
        target_statement_number=target_statement_number,
        attempt_number=getattr(state, 'current_attempt', 1),
        migration_name=state.migration_name,
        schema_name=state.schema_name,
        object_name=state.object_name,
        object_type=state.objecttype,
        cloud_category=state.cloud_category,
        execution_context=execution_context
    )

    return tracker


def initialize_stage2_pipeline_execution_tracking(state: Stage2WorkflowState) -> Stage2PipelineExecutionTracker:
    """
    Initialize Stage2 pipeline execution tracking for a statement processing session.

    IMPORTANT: This should only be called once per statement processing. Subsequent calls
    should use get_or_create_stage2_pipeline_execution_tracker to reuse the existing tracker.

    Args:
        state: Stage2 workflow state

    Returns:
        Initialized pipeline execution tracker with initial input logged
    """
    execution_source = getattr(state, 'execution_source', 'unknown')
    current_attempt = getattr(state, 'current_attempt', 1)
    current_statement_index = getattr(state, 'current_statement_index', 0)

    print(f"🔧 Initializing Stage2 pipeline execution tracking")
    print(f"   - Statement: {current_statement_index + 1}, Attempt: {current_attempt}")
    print(f"   - Execution source: {execution_source}")

    # Create pipeline execution tracker
    pipeline_execution_tracker = create_stage2_pipeline_execution_tracker(state)

    # Get current statement data for initial input
    available_features_with_statements = getattr(state, 'available_features_with_statements', [])

    if current_statement_index < len(available_features_with_statements):
        current_statement = available_features_with_statements[current_statement_index]

        # Log initial pipeline input (only if this is a fresh tracker)
        pipeline_execution_tracker.log_initial_pipeline_input(
            original_oracle_statement=current_statement.get('original_source_statement', ''),
            qmigrator_converted_statement=current_statement.get('statement_after_typecasting',
                                                              current_statement.get('original_source_statement', ''))
        )

    # Store tracker in state for reuse across methods
    state.stage2_pipeline_execution_tracker = pipeline_execution_tracker
    print(f"💾 Stored pipeline execution tracker in state (id: {id(pipeline_execution_tracker)})")

    return pipeline_execution_tracker


def get_or_create_stage2_pipeline_execution_tracker(state: Stage2WorkflowState) -> Stage2PipelineExecutionTracker:
    """
    Get existing Stage2 pipeline execution tracker from state or create new one.

    IMPORTANT: Once a tracker is created for a statement, it should be reused throughout
    the entire statement processing to preserve all pipeline execution logs (PRE_PROCESSING,
    RESPONSIBLE, POST_PROCESSING phases).

    Args:
        state: Stage2 workflow state

    Returns:
        Pipeline execution tracker instance
    """
    execution_source = getattr(state, 'execution_source', 'unknown')
    has_tracker = hasattr(state, 'stage2_pipeline_execution_tracker')
    tracker_exists = has_tracker and state.stage2_pipeline_execution_tracker is not None

    print(f"🔍 Pipeline tracker check:")
    print(f"   - Execution source: {execution_source}")
    print(f"   - Has tracker attribute: {has_tracker}")
    print(f"   - Tracker exists: {tracker_exists}")

    # Check if tracker already exists in state - ALWAYS reuse existing tracker
    if tracker_exists:
        tracker_id = id(state.stage2_pipeline_execution_tracker)
        print(f"🔄 Reusing existing Stage2 pipeline execution tracker (id: {tracker_id})")
        return state.stage2_pipeline_execution_tracker

    # Create new tracker only if none exists
    print(f"🆕 Creating new Stage2 pipeline execution tracker")
    return initialize_stage2_pipeline_execution_tracking(state)


def finalize_stage2_pipeline_execution_tracking(state: Stage2WorkflowState, final_pipeline_output: str,
                                              pipeline_execution_status: str = "SUCCESS"):
    """
    Finalize Stage2 pipeline execution tracking and save the log file.

    Args:
        state: Stage2 workflow state
        final_pipeline_output: Final pipeline output
        pipeline_execution_status: Pipeline execution status
    """
    pipeline_execution_tracker = getattr(state, 'stage2_pipeline_execution_tracker', None)
    if pipeline_execution_tracker:
        pipeline_execution_tracker.set_final_pipeline_output(final_pipeline_output, pipeline_execution_status)
        pipeline_execution_tracker.save_pipeline_execution_log()
        print(f"✅ Stage2 pipeline execution tracking finalized for statement {pipeline_execution_tracker.target_statement_number}")
    else:
        print("⚠️ No Stage2 pipeline execution tracker found in state - tracking not available")


def clear_stage2_pipeline_execution_tracker(state: Stage2WorkflowState):
    """
    Clear Stage2 pipeline execution tracker from state (for new statement processing).

    Args:
        state: Stage2 workflow state
    """
    if hasattr(state, 'stage2_pipeline_execution_tracker'):
        state.stage2_pipeline_execution_tracker = None
        print("🧹 Cleared Stage2 pipeline execution tracker from state")
