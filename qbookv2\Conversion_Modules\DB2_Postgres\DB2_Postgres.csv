Migration_Name,Feature_Name,Keywords,Predecessor,Estimations,Automation_Percentage,Object_Path
DB2_Postgres,Cursor,cursor,Procedure_structure,1,60,Procedure/Pre
DB2_Postgres,Merge,"merge into , when matched then update , when not matched then insert",No Predecessor,8,70,Common/Statement/Pre
DB2_Postgres,Rowid,rowid,No Predecessor,5,60,Common/Statement/Pre
DB2_Postgres,Currentdatabase,CURRENT SERVER,No Predecessor,5,60,Common/Statement/Pre
DB2_Postgres,Timestamp,timestamp,No Predecessor,5,60,Common/Statement/Pre
DB2_Postgres,Datatypes,clob|blob|nchar|vargraphic|nvarchar|character varying|varchar|for bit data|character|graphic|char|float|double precision|decfloat|integer generated by default as identity|int generated by default as identity|number generated by default as identity|generated by default as identity|generated always as identity|serial|current_timestamp|current|integer|small int|to_number,No Predecessor,5,70,Common/Pre
DB2_Postgres,Trigger,Trigger,No Predecessor,5,60,Trigger/Pre
DB2_Postgres,Coalesce,nvl|ifnull,No Predecessor,5,70,Common/Statement/Pre
DB2_Postgres,Listagg,listagg,No Predecessor,5,60,Common/Statement/Pre
DB2_Postgres,Variableset,set,Procedure_structure,1,70,Procedure/Pre
DB2_Postgres,Xml,XMLCAST|XMLQUERY,No Predecessor,5,70,Common/Statement/Pre
DB2_Postgres,Variableset,set,Function_structure,1,70,Function/Pre
DB2_Postgres,Decode,decode,No Predecessor,5,70,Common/Statement/Pre
DB2_Postgres,Minus,minus,No Predecessor,5,60,Common/Statement/Pre
DB2_Postgres,Nextval,nextval for|nextvalfor|nextval,No Predecessor,5,70,Common/Statement/Pre
DB2_Postgres,Datechange,year|month|day|date|hour|minute|second,No Predecessor,5,70,Common/Statement/Pre
DB2_Postgres,Function_structure,function,No Predecessor,5,75,Function/Pre
DB2_Postgres,Sysdummy,SYSIBM|sysdummy1,No Predecessor,5,60,Common/Statement/Pre
DB2_Postgres,Inout,in|out,Procedure_structure,5,60,Procedure/Pre
DB2_Postgres,Procedure_structure,language sql|procedure,No Predecessor,1,60,Procedure/Pre
DB2_Postgres,Table,clob|blob|nchar|vargraphic|nvarchar|character varying|varchar|for bit data|character|graphic|char|float|double precision|decfloat|integer generated by default as identity|int generated by default as identity|number generated by default as identity|generated by default as identity|generated always as identity|serial|current_timestamp|current|integer|small int|to_number|table,No Predecessor,5,80,Table/Pre
DB2_Postgres,Datatypes,clob|blob|nchar|vargraphic|nvarchar|character varying|varchar|for bit data|character|graphic|char|float|double precision|decfloat|integer generated by default as identity|int generated by default as identity|number generated by default as identity|generated by default as identity|generated always as identity|serial|current_timestamp|current|integer|small int|to_number|table,No Predecessor,5,80,View/Pre
DB2_Postgres,Datatypes,clob|blob|nchar|vargraphic|nvarchar|character varying|varchar|for bit data|character|graphic|char|float|double precision|decfloat|integer generated by default as identity|int generated by default as identity|number generated by default as identity|generated by default as identity|generated always as identity|serial|current_timestamp|current|integer|small int|to_number|table,No Predecessor,5,80,Not_Null_Constraint/Pre
DB2_Postgres,Datatypes,clob|blob|nchar|vargraphic|nvarchar|character varying|varchar|for bit data|character|graphic|char|float|double precision|decfloat|integer generated by default as identity|int generated by default as identity|number generated by default as identity|generated by default as identity|generated always as identity|serial|current_timestamp|current|integer|small int|to_number|table,No Predecessor,5,80,Default_Constraint/Pre
DB2_Postgres,Datatypes,clob|blob|nchar|vargraphic|nvarchar|character varying|varchar|for bit data|character|graphic|char|float|double precision|decfloat|integer generated by default as identity|int generated by default as identity|number generated by default as identity|generated by default as identity|generated always as identity|serial|current_timestamp|current|integer|small int|to_number|table,No Predecessor,5,80,Check_Constraint/Pre
DB2_Postgres,Raisenoticedbms,DBMS_OUTPUT|PUT_LINE,No Predecessor,10,60,Common/Statement/Pre
DB2_Postgres,While_loop,while,No Predecessor,10,70,Common/Statement/Pre
DB2_Postgres,Declare_variables,declare,No Predecessor,10,60,Common/Pre
DB2_Postgres,Default_null,default,No Predecessor,10,65,Common/Pre
DB2_Postgres,Raisenotice,signal,No Predecessor,10,60,Common/Statement/Pre
DB2_Postgres,View,view,No Predecessor,10,65,View/Pre
DB2_Postgres,Char_function,char,No Predecessor,10,70,Common/Pre
DB2_Postgres,Exception_handler,handler,No Predecessor,10,70,Common/Pre
DB2_Postgres,For_loop,for,No Predecessor,10,65,Common/Pre
DB2_Postgres,Labeled_loop,loop|leave,No Predecessor,10,65,Common/Statement/Pre
