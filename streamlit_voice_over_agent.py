"""
Streamlit Application for Voice Over Agent

This application provides a user-friendly interface for the Voice Over Agent
for converting video voice-overs using Azure AI Speech Services.
"""

import streamlit as st
import requests
import time
from typing import Optional
from config import Config

# Page configuration
st.set_page_config(
    page_title="🎤 Voice Over Agent",
    page_icon="🎤",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .section-header {
        font-size: 1.5rem;
        font-weight: bold;
        color: #ff7f0e;
        margin-top: 2rem;
        margin-bottom: 1rem;
    }
    .success-box {
        padding: 1rem;
        border-radius: 0.5rem;
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
        margin: 1rem 0;
    }
    .error-box {
        padding: 1rem;
        border-radius: 0.5rem;
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
        margin: 1rem 0;
    }
    .info-box {
        padding: 1rem;
        border-radius: 0.5rem;
        background-color: #d1ecf1;
        border: 1px solid #bee5eb;
        color: #0c5460;
        margin: 1rem 0;
    }
    .stButton > button {
        width: 100%;
        background-color: #007bff;
        color: white;
        border: none;
        padding: 0.5rem 1rem;
        border-radius: 0.25rem;
        font-weight: bold;
    }
    .stButton > button:hover {
        background-color: #0056b3;
    }
</style>
""", unsafe_allow_html=True)

def main():
    """Main Streamlit application"""
    
    # Header
    st.markdown('<div class="main-header">🎤 Voice Over Agent</div>', unsafe_allow_html=True)
    st.markdown("---")
    
    # Sidebar for configuration
    with st.sidebar:
        st.markdown("### ⚙️ Configuration")
        
        # API Configuration
        api_host = st.text_input(
            "API Host",
            value="http://localhost:8000",
            help="Base URL for the Voice Over Agent API"
        )
        
        # Voice Selection
        st.markdown("### 🎵 Voice Options")
        voice_options = {
            "aria": "en-US-AriaNeural (Female, Natural)",
            "jenny": "en-US-JennyNeural (Female, Friendly)", 
            "guy": "en-US-GuyNeural (Male, Professional)",
            "davis": "en-US-DavisNeural (Male, Warm)",
            "jane": "en-US-JaneNeural (Female, Confident)"
        }
        
        selected_voice = st.selectbox(
            "Select Voice",
            options=list(voice_options.keys()),
            format_func=lambda x: voice_options[x],
            index=0
        )
        
        # Azure Configuration Status
        st.markdown("### 🔧 Azure Services")
        if hasattr(Config, 'AZURE_SPEECH_KEY') and Config.AZURE_SPEECH_KEY:
            st.success("✅ Azure Speech Services Configured")
        else:
            st.error("❌ Azure Speech Services Not Configured")
            
        if hasattr(Config, 'AZURE_STORAGE_CONNECTION_STRING') and Config.AZURE_STORAGE_CONNECTION_STRING:
            st.success("✅ Azure Storage Configured")
        else:
            st.error("❌ Azure Storage Not Configured")
    
    # Main content area
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.markdown('<div class="section-header">📹 Video Input</div>', unsafe_allow_html=True)
        
        # Video URL input
        video_input = st.text_input(
            "Video URL",
            placeholder="https://example.com/video.mp4",
            help="Enter the URL of the video you want to process"
        )

        if video_input:
            st.info(f"📹 Video URL: {video_input}")
        
        # Process button
        if st.button("🚀 Start Voice Over Conversion", type="primary"):
            if not video_input:
                st.error("❌ Please provide a video URL")
            else:
                process_video(api_host, video_input, selected_voice)
    
    with col2:
        st.markdown('<div class="section-header">ℹ️ Information</div>', unsafe_allow_html=True)
        
        st.markdown("""
        <div class="info-box">
        <h4>🎯 What this does:</h4>
        <ul>
            <li>Downloads video from URL</li>
            <li>Extracts audio track</li>
            <li>Transcribes speech using Azure AI</li>
            <li>Generates new AI voice</li>
            <li>Replaces original audio</li>
            <li>Outputs final video</li>
        </ul>
        </div>
        """, unsafe_allow_html=True)
        
        st.markdown("""
        <div class="info-box">
        <h4>📋 Supported formats:</h4>
        <ul>
            <li>Video: MP4, AVI, MOV, MKV, WebM</li>
            <li>Audio: Automatic extraction</li>
            <li>Languages: English (en-US)</li>
        </ul>
        </div>
        """, unsafe_allow_html=True)

def process_video(api_host: str, video_url: str, voice_name: str):
    """Process video through the Voice Over Agent API"""

    try:
        # Prepare API request
        api_url = f"{api_host}/ai/voice-over/process"
        payload = {
            "video_url": video_url,
            "voice_name": voice_name,
            "request_id": f"streamlit_{int(time.time())}"
        }

        with st.spinner("🔄 Starting voice over conversion..."):
            # Make API request to start background processing
            response = requests.post(api_url, json=payload, timeout=30)

        if response.status_code == 200:
            result = response.json()

            if result.get("success"):
                st.success(f"✅ {result.get('message', 'Voice over conversion started successfully!')}")
                st.info(f"🆔 Request ID: {result.get('request_id')}")
                st.info("📋 Processing is running in the background. Check your output folder for results.")

            else:
                st.error(f"❌ Failed to start processing: {result.get('message', 'Unknown error')}")

        else:
            st.error(f"❌ API Error: {response.status_code} - {response.text}")

    except requests.exceptions.Timeout:
        st.error("⏰ Request timed out. Please try again.")
    except requests.exceptions.RequestException as e:
        st.error(f"🌐 Network error: {str(e)}")
    except Exception as e:
        st.error(f"💥 Unexpected error: {str(e)}")

if __name__ == "__main__":
    main()
