"""
Stage2 Pipeline Execution Tracker
Comprehensive tracking and logging for Stage2 feature pipeline execution
"""

import os
from datetime import datetime
from config import Config


class Stage2PipelineExecutionTracker:
    """
    Comprehensive Stage2 Pipeline Execution Tracker.
    
    Tracks complete pipeline execution flow including:
    - Pre-processing features execution
    - Responsible features execution  
    - Post-processing features execution
    - Module source tracking (Registry vs Original vs Enhanced)
    - Execution context detection (Direct vs Enhancement pipeline)
    - Developer insights and performance context
    """
    
    def __init__(self, target_statement_number: int, attempt_number: int, 
                 migration_name: str, schema_name: str, object_name: str, 
                 object_type: str, cloud_category: str, execution_context: str = "Unknown"):
        """
        Initialize Stage2 Pipeline Execution Tracker.
        
        Args:
            target_statement_number: Target statement number being processed
            attempt_number: Current attempt number
            migration_name: Migration name (e.g., Oracle_Postgres14)
            schema_name: Schema name (e.g., AICCTEST)
            object_name: Object name (e.g., P_CONFIRM_MULTI_WAITLIST)
            object_type: Object type (e.g., Procedure, Function, Package)
            cloud_category: Cloud category (local/cloud)
            execution_context: Execution context for developer insight
        """
        self.target_statement_number = target_statement_number
        self.attempt_number = attempt_number
        self.migration_name = migration_name
        self.schema_name = schema_name
        self.object_name = object_name
        self.object_type = object_type
        self.cloud_category = cloud_category
        self.execution_context = execution_context
        
        # Get QBook base path
        if cloud_category.lower() == 'local':
            self.qbook_base_path = Config.Qbook_Local_Path
        else:
            self.qbook_base_path = Config.Qbook_Path
        
        # Pipeline logs directory (QBook structure)
        self.pipeline_logs_dir = os.path.join(
            self.qbook_base_path,
            "Stage1_Metadata",
            migration_name,
            schema_name,
            object_type,
            object_name,
            "pipeline_logs"
        )
        
        # Pipeline log file (define before setup to avoid attribute error)
        log_filename = f"statement_{target_statement_number}_attempt_{attempt_number}_pipeline_execution.txt"
        self.log_file_path = os.path.join(self.pipeline_logs_dir, log_filename)

        # Setup logging directory and cleanup
        self.setup_pipeline_logging_directory()
        
        # Execution tracking
        self.feature_executions = []
        self.original_oracle_statement = ""
        self.qmigrator_converted_statement = ""
        self.final_pipeline_output = ""
        self.pipeline_execution_status = "IN_PROGRESS"
        
        print(f"📝 Stage2 Pipeline Execution Tracker initialized: {self.log_file_path}")
        print(f"🔄 Execution Context: {self.execution_context}")
    
    def setup_pipeline_logging_directory(self):
        """Setup pipeline logging directory and preserve existing logs during tracker recreation."""
        try:
            # Create pipeline logs directory
            os.makedirs(self.pipeline_logs_dir, exist_ok=True)

            # Check if log file for this statement/attempt already exists
            log_exists = os.path.exists(self.log_file_path)

            if not log_exists:
                # Only cleanup OTHER log files if this is a fresh statement/attempt
                # This allows multiple attempts while preserving current attempt logs
                if os.path.exists(self.pipeline_logs_dir):
                    for filename in os.listdir(self.pipeline_logs_dir):
                        if filename.endswith('_pipeline_execution.txt') and filename != os.path.basename(self.log_file_path):
                            file_path = os.path.join(self.pipeline_logs_dir, filename)
                            os.remove(file_path)
                            print(f"🗑️ Removed old pipeline execution log: {filename}")
                print(f"📁 Pipeline execution logs directory ready: {self.pipeline_logs_dir}")
            else:
                print(f"📁 Preserving existing pipeline execution log: {os.path.basename(self.log_file_path)}")
                print(f"⚠️ WARNING: Tracker recreated for existing statement/attempt - this may indicate a workflow issue")

        except Exception as e:
            print(f"⚠️ Warning: Failed to setup pipeline logging directory: {str(e)}")
            # Fallback to temp directory
            self.pipeline_logs_dir = os.path.join(os.getcwd(), "temp_pipeline_execution_logs")
            os.makedirs(self.pipeline_logs_dir, exist_ok=True)
    
    def log_initial_pipeline_input(self, original_oracle_statement: str, qmigrator_converted_statement: str):
        """
        Log initial pipeline input statements.
        
        Args:
            original_oracle_statement: Original Oracle source statement
            qmigrator_converted_statement: QMigrator converted statement after typecasting
        """
        self.original_oracle_statement = original_oracle_statement
        self.qmigrator_converted_statement = qmigrator_converted_statement
        print(f"📋 Logged initial pipeline input for statement {self.target_statement_number}")
    
    def log_feature_pipeline_execution(self, feature_name: str, module_source_type: str, 
                                     module_relative_path: str, feature_input_statement: str, 
                                     feature_output_statement: str, execution_phase: str = "UNKNOWN"):
        """
        Log individual feature execution in the pipeline.
        
        Args:
            feature_name: Name of the feature (e.g., 'datatypes', 'record', 'nvl_function')
            module_source_type: Module source type (enhanced_registry, original, enhanced)
            module_relative_path: Relative path to the module (e.g., 'Common/Pre/datatypes.py')
            feature_input_statement: Input statement to the feature
            feature_output_statement: Output statement from the feature
            execution_phase: Execution phase (PRE_PROCESSING, RESPONSIBLE, POST_PROCESSING)
        """
        # Determine full module path for developer insight
        full_module_path = self.determine_full_module_path(module_source_type, module_relative_path)
        
        feature_execution_entry = {
            'execution_sequence': len(self.feature_executions) + 1,
            'feature_name': feature_name,
            'execution_phase': execution_phase,
            'module_source_type': module_source_type,
            'module_relative_path': module_relative_path,
            'module_full_path': full_module_path,
            'feature_input_statement': feature_input_statement,
            'feature_output_statement': feature_output_statement,
            'transformation_applied': 'Yes' if feature_input_statement != feature_output_statement else 'No',
            'input_statement_length': len(feature_input_statement),
            'output_statement_length': len(feature_output_statement)
        }
        
        self.feature_executions.append(feature_execution_entry)
        print(f"📊 Logged feature execution {feature_execution_entry['execution_sequence']}: {feature_name} ({execution_phase}) - {module_source_type} - Transformation: {feature_execution_entry['transformation_applied']}")
    
    def determine_full_module_path(self, module_source_type: str, module_relative_path: str) -> str:
        """
        Determine full module path based on source type for developer insight.
        
        Args:
            module_source_type: Module source type (enhanced_registry, original, enhanced)
            module_relative_path: Relative module path
            
        Returns:
            Full absolute path to the module
        """
        if module_source_type == 'enhanced_registry':
            # Enhanced registry path
            return os.path.join(
                self.qbook_base_path,
                "Stage1_Metadata",
                self.migration_name,
                "Enhanced_Modules_Registry",
                module_relative_path
            )
        elif module_source_type == 'enhanced':
            # Enhanced module from current processing
            module_filename = module_relative_path.split('/')[-1].replace('.py', '')
            enhanced_filename = f"{module_filename}_attempt_{self.attempt_number}.py"
            return os.path.join(
                self.qbook_base_path,
                "Stage1_Metadata",
                self.migration_name,
                self.schema_name,
                self.object_type,
                self.object_name,
                "feature_modules",
                str(self.attempt_number),
                enhanced_filename
            )
        else:
            # Original module
            return os.path.join(
                self.qbook_base_path,
                "Conversion_Modules",
                self.migration_name,
                module_relative_path
            )
    
    def set_final_pipeline_output(self, final_pipeline_output: str, pipeline_execution_status: str = "SUCCESS"):
        """
        Set the final pipeline output and execution status.

        Args:
            final_pipeline_output: Final pipeline output statement
            pipeline_execution_status: Pipeline execution status (SUCCESS/FAILED)
        """
        self.final_pipeline_output = final_pipeline_output
        self.pipeline_execution_status = pipeline_execution_status

    def save_pipeline_execution_log(self):
        """Save simple pipeline execution log for developers."""
        try:
            with open(self.log_file_path, 'w', encoding='utf-8') as f:
                # Simple header
                f.write("STAGE2 PIPELINE EXECUTION LOG\n")
                f.write("=" * 100 + "\n")
                f.write(f"Target Statement Number: {self.target_statement_number}\n")
                f.write(f"Attempt Number: {self.attempt_number}\n")
                f.write(f"Execution Context: {self.execution_context}\n")
                f.write(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"Migration: {self.migration_name}\n")
                f.write(f"Schema: {self.schema_name}, Object: {self.object_name} ({self.object_type})\n")
                f.write(f"Cloud Category: {self.cloud_category}\n\n")

                # Initial Input
                f.write("INITIAL INPUT:\n")
                f.write("-" * 20 + "\n")
                f.write("Original Oracle:\n")
                f.write(f"{self.original_oracle_statement}\n\n")
                f.write("QMigrator Converted After Preprocessing:\n")
                f.write(f"{self.qmigrator_converted_statement}\n\n")

                # Feature Execution Details
                f.write("FEATURE EXECUTION:\n")
                f.write("-" * 20 + "\n")

                current_phase = ""
                for execution in self.feature_executions:
                    # Add phase separator
                    if execution['execution_phase'] != current_phase:
                        current_phase = execution['execution_phase']
                        f.write(f"\n>>> {current_phase} PHASE <<<\n")
                        f.write("=" * 40 + "\n")

                    f.write(f"\n{execution['execution_sequence']}. {execution['feature_name']} ({execution['module_source_type']})\n")
                    f.write(f"Path: {execution['module_relative_path']}\n")
                    f.write(f"Changed: {execution['transformation_applied']}\n")

                    f.write("INPUT:\n")
                    f.write(f"{execution['feature_input_statement']}\n")

                    f.write("OUTPUT:\n")
                    f.write(f"{execution['feature_output_statement']}\n")
                    f.write("-" * 40 + "\n")

                # Final Output
                f.write("\nFINAL OUTPUT:\n")
                f.write("-" * 20 + "\n")
                f.write(f"{self.final_pipeline_output}\n\n")

                # Simple Summary
                f.write("SUMMARY:\n")
                f.write("-" * 10 + "\n")
                f.write(f"Total Features: {len(self.feature_executions)}\n")
                f.write(f"Status: {self.pipeline_execution_status}\n")

                # Count by phase
                phase_counts = {}
                for execution in self.feature_executions:
                    phase = execution['execution_phase']
                    phase_counts[phase] = phase_counts.get(phase, 0) + 1

                f.write("Phase Breakdown:\n")
                for phase, count in phase_counts.items():
                    f.write(f"  - {phase}: {count} modules\n")

            print(f"💾 Pipeline execution log saved: {self.log_file_path}")

        except Exception as e:
            print(f"❌ Error saving pipeline execution log: {str(e)}")


