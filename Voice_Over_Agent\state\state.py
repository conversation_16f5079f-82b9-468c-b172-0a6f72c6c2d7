"""
Voice Over Agent State Management

This module defines the state structures for voice over conversion processing
using Pydantic models for validation and serialization.
"""

from typing import Optional
from pydantic import BaseModel, Field


class VoiceOverResponse(BaseModel):
    """Response model for voice over conversion processing results"""
    
    success: bool = Field(..., description="Whether the conversion was successful")
    output_url: Optional[str] = Field(default=None, description="URL of the processed video")
    error_message: Optional[str] = Field(default=None, description="Error message if conversion failed")
    processing_time: Optional[float] = Field(default=None, description="Processing time in seconds")


class PolishedTranscriptResponse(BaseModel):
    """Response model for AI-powered transcript polishing using LLM structured output"""
    
    polished_transcript: str = Field(description="The transcript, polished for grammar and clarity, same length as input.")