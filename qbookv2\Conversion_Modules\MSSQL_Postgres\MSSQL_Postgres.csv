Migration_Name,Feature_Name,Keywords,Predecessor,Estimations,Automation_Percentage,Object_Path
MSSQL_Postgres,Input_parameters_fun,create|function,Post_functions,50,70,Function/Post
MSSQL_Postgres,Primarykey,Null,No Predecessor,2,50,Primary_Key
MSSQL_Postgres,Psqlconcat,pSQL,Post_procedure,10,60,Procedure/Post
MSSQL_Postgres,Ifendif,if|else,Post_procedure,10,70,Procedure/Post
MSSQL_Postgres,Concatassign,pSQL,Psqlconcat,10,60,Procedure/Post
MSSQL_Postgres,Default,Procedure,Post_procedure,10,65,Procedure/Post
MSSQL_Postgres,Post_procedure,create or replace|create or alter|create,No Predecessor,80,70,Procedure/Post
MSSQL_Postgres,Notnullconstraint,alter table,No Predecessor,15,80,Not_Null_Constraint/Pre
MSSQL_Postgres,Declare_variable,declare,Input_parameters_fun,50,80,Function/Post
MSSQL_Postgres,Set_keyword,set|create,No Predecessor,60,70,Function/Statement/Pre
MSSQL_Postgres,Input_parameters_proc,create|procedure,Post_procedure,50,70,Procedure/Post
MSSQL_Postgres,Post_functions,create or replace|create function,No Predecessor,60,70,Function/Post
MSSQL_Postgres,Getdate,getdate,No Predecessor,10,75,Default_Constraint/Pre
MSSQL_Postgres,Casttoxml,CHARINDEX,No Predecessor,10,65,Common/Pre
MSSQL_Postgres,Set_keyword,set|create,No Predecessor,20,70,Common/Pre
MSSQL_Postgres,Datatypes,BIGINT|IDENTITY|BIT|CHAR|DATETIME|decimal|Float|image|int||money|NCHAR|numeric|real|smalldatetime|smallmoney|smallint|Nvarchar|BINARY|text|TIMESTAMP|tinyint|VARCHAR|xml|SYSNAME|datetimeoffset|time,No Predecessor,1,100,Default_Constraint/Pre
MSSQL_Postgres,Datatypes,BIGINT|IDENTITY|BIT|CHAR|DATETIME|decimal|Float|image|int||money|NCHAR|numeric|real|smalldatetime|smallmoney|smallint|Nvarchar|BINARY|text|TIMESTAMP|tinyint|VARCHAR|xml|SYSNAME|datetimeoffset|time,No Predecessor,1,100,Not_Null_Constraint/Pre
MSSQL_Postgres,To_char,convert(*),No Predecessor,5,90,Common/Pre
MSSQL_Postgres,Aliasnameforxml,select,No Predecessor,10,70,Common/Pre
MSSQL_Postgres,Date_add,dateadd(*),No Predecessor,10,70,Common/Pre
MSSQL_Postgres,Date_diff,datediff(*),No Predecessor,10,70,Common/Pre
MSSQL_Postgres,Equal_to_from,select * from,No Predecessor,10,90,Common/Pre
MSSQL_Postgres,Exception,BEGIN&TRY|end&TRY|BEGIN&catch|end&catch,No Predecessor,10,65,Common/Pre
MSSQL_Postgres,Exec,exec,No Predecessor,7,65,Common/Pre
MSSQL_Postgres,Extract,extract,No Predecessor,10,55,Common/Pre
MSSQL_Postgres,Format,format(*),No Predecessor,5,90,Common/Pre
MSSQL_Postgres,Getdate,getdate,No Predecessor,10,60,Common/Pre
MSSQL_Postgres,Iif,Iif,No Predecessor,10,65,Common/Pre
MSSQL_Postgres,Isnull,Isnull,No Predecessor,2,80,Common/Pre
MSSQL_Postgres,Length,len,No Predecessor,10,65,Common/Pre
MSSQL_Postgres,Ifsinglelinewithset,if|set,No Predecessor,10,65,Common/Pre
MSSQL_Postgres,Savepoint,ROLLBACK|TRANSACTION|SAVE|TRANSACTION,No Predecessor,10,65,Common/Pre
MSSQL_Postgres,Print,print,No Predecessor,10,65,Common/Pre
MSSQL_Postgres,Sqlrowcount,ROWCOUNT,No Predecessor,10,65,Common/Pre
MSSQL_Postgres,Stringagg,string_agg,No Predecessor,10,65,Common/Pre
MSSQL_Postgres,Stuff,stuff,No Predecessor,10,65,Common/Pre
MSSQL_Postgres,Tablename,table,No Predecessor,7,60,Common/Pre
MSSQL_Postgres,Temp_table,where&into,No Predecessor,5,80,Common/Pre
MSSQL_Postgres,Nextval,next&val&for,No Predecessor,10,65,Common/Pre
MSSQL_Postgres,Year,year(*),No Predecessor,10,80,Common/Pre
MSSQL_Postgres,Whileloop,while,No Predecessor,10,65,Common/Pre
MSSQL_Postgres,Afterbody,Procedure,Post_procedure,10,60,Procedure/Post
MSSQL_Postgres,Askeyword,as,Psqlconcat,10,60,Procedure/Post
MSSQL_Postgres,Curval,current_value&cast&sequences,No Predecessor,10,65,Common/Pre
MSSQL_Postgres,Top,top,No Predecessor,10,65,Common/Pre
MSSQL_Postgres,Datatypes,datetimeoffset|nvarchar|datetime|time|bit|binary|varchar|nvarchar|ntext|varbinary|uniqueidentifier|tinyint|rowversion|int,No Predecessor,30,90,Table/Pre
MSSQL_Postgres,Integeridentity,identity,No Predecessor,10,65,Common/Pre
MSSQL_Postgres,Insertwithoutcolumns,insert,No Predecessor,10,65,Common/Pre
MSSQL_Postgres,Afterbody,Function,Post_functions,2,60,Function/Post
MSSQL_Postgres,Psqlconcat,pSQL,Post_functions,2,60,Function/Post
MSSQL_Postgres,Ifendif,if|else,Post_functions,10,60,Function/Post
MSSQL_Postgres,Default,procedure|function,Post_functions,10,60,Function/Post
