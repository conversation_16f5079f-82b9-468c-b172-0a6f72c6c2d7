"""
Video Composer Module for Voice Over Agent

This module handles video composition by replacing original audio with AI-generated audio,
ensuring proper synchronization and high-quality output.
"""

from typing import Optional
from moviepy.video.io.VideoFileClip import VideoFileClip
from moviepy.audio.io.AudioFileClip import AudioFileClip


class VideoComposer:
    """
    Handles video composition by replacing audio tracks with AI-generated audio.
    
    Features:
    - Audio replacement with automatic duration matching
    - High-quality video encoding with AAC audio and H.264 video
    - Proper resource management and cleanup
    - Duration synchronization between video and audio
    - Comprehensive error handling and logging
    """
    
    def __init__(self):
        """Initialize the VideoComposer with default encoding settings."""
        self.video_codec = 'libx264'
        self.audio_codec = 'aac'
    
    def replace_audio_in_video(self, video_path: str, new_audio_path: str, output_path: str) -> bool:
        """
        Replace audio in video with new AI-generated audio while maintaining synchronization.
        
        Args:
            video_path (str): Path to the original video file
            new_audio_path (str): Path to the new AI-generated audio file
            output_path (str): Path where the final video will be saved
            
        Returns:
            bool: True if composition successful, False otherwise
            
        Features:
        - Automatic duration matching between video and audio
        - High-quality encoding with industry-standard codecs
        - Proper handling of duration mismatches
        - Memory-efficient processing with resource cleanup
        """
        video = None
        new_audio = None
        final_video = None
        
        try:
            print("🔧 Starting video composition with AI audio...")

            # Load video and audio files
            video = VideoFileClip(video_path)
            new_audio = AudioFileClip(new_audio_path)

            # Log duration information
            video_duration = video.duration
            audio_duration = new_audio.duration
            print(f"📊 Video duration: {video_duration:.2f}s")
            print(f"📊 Audio duration: {audio_duration:.2f}s")

            # Handle duration synchronization
            synchronized_audio = self._synchronize_audio_duration(new_audio, video_duration)

            # Create final video with new audio
            final_video = video.with_audio(synchronized_audio)
            
            # Write final video with high-quality settings
            print("🔧 Encoding final video with AI audio...")
            final_video.write_videofile(
                output_path,
                audio_codec=self.audio_codec,
                codec=self.video_codec,
                temp_audiofile='temp-audio.m4a',
                remove_temp=True
            )

            print(f"✅ Video composition completed successfully: {output_path}")
            return True

        except Exception as e:
            print(f"❌ Error in video composition: {str(e)}")
            return False
        
        finally:
            # Ensure proper cleanup of all resources
            self._cleanup_resources(video, new_audio, final_video)
    
    def _synchronize_audio_duration(self, audio: AudioFileClip, target_duration: float) -> AudioFileClip:
        """
        Synchronize audio duration with video duration.
        
        Args:
            audio (AudioFileClip): Audio clip to synchronize
            target_duration (float): Target duration in seconds
            
        Returns:
            AudioFileClip: Synchronized audio clip
        """
        audio_duration = audio.duration
        
        if audio_duration > target_duration:
            # Trim audio if it's longer than video
            synchronized_audio = audio.subclip(0, target_duration)
            print(f"🔧 Audio trimmed from {audio_duration:.2f}s to {target_duration:.2f}s")
        elif audio_duration < target_duration:
            # Keep audio as-is if shorter (video will have silence at the end)
            synchronized_audio = audio
            print(f"⚠️ Audio is {target_duration - audio_duration:.2f}s shorter than video")
            print("🔧 Video will have silence at the end")
        else:
            # Perfect match
            synchronized_audio = audio
            print("✅ Audio and video durations match perfectly")
        
        return synchronized_audio
    
    def _cleanup_resources(self, *resources):
        """
        Clean up video and audio resources to prevent memory leaks.
        
        Args:
            *resources: Variable number of MoviePy clip objects to clean up
        """
        for resource in resources:
            if resource is not None:
                try:
                    resource.close()
                except Exception as e:
                    print(f"⚠️ Warning: Could not clean up resource: {str(e)}")
    
    def validate_input_files(self, video_path: str, audio_path: str) -> bool:
        """
        Validate input video and audio files before composition.
        
        Args:
            video_path (str): Path to the video file
            audio_path (str): Path to the audio file
            
        Returns:
            bool: True if both files are valid, False otherwise
        """
        try:
            # Test video file
            test_video = VideoFileClip(video_path)
            video_duration = test_video.duration
            test_video.close()
            
            if video_duration <= 0:
                print(f"❌ Invalid video duration: {video_duration}")
                return False
            
            # Test audio file
            test_audio = AudioFileClip(audio_path)
            audio_duration = test_audio.duration
            test_audio.close()
            
            if audio_duration <= 0:
                print(f"❌ Invalid audio duration: {audio_duration}")
                return False
            
            print(f"✅ Input files validated - Video: {video_duration:.2f}s, Audio: {audio_duration:.2f}s")
            return True
            
        except Exception as e:
            print(f"❌ Error validating input files: {str(e)}")
            return False