"""
FastAPI Routes for Voice Over Agent

This module provides REST API endpoints for the Voice Over Agent
for converting video voice-overs using Azure AI Speech Services.
Uses form data input for better compatibility with complex URLs.
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks, Form
from typing import Optional
import uuid
from Voice_Over_Agent.utils.voice_processor import process_voice_over_request
from config import Config

router = APIRouter()

def process_voice_over_background(video_url: str, voice_name: Optional[str] = None, request_id: Optional[str] = None):
    """
    Background task for processing voice over conversion
    
    Args:
        video_url (str): URL of the video to process
        voice_name (Optional[str]): Azure neural voice name to use
        request_id (Optional[str]): Request ID for tracking
        
    Returns:
        dict: Processing result with success status and details
    """
    try:
        print(f"🔄 Starting background processing for request: {request_id}")
        
        # Process the voice over request
        result = process_voice_over_request(
            video_url=video_url,
            voice_name=voice_name
        )
        
        print(f"✅ Background processing completed for request: {request_id}")
        return result
    except Exception as e:
        print(f"❌ Background processing failed for request {request_id}: {str(e)}")
        return {
            "success": False,
            "error_message": f"Voice over conversion failed: {str(e)}"
        }

@router.post("/voice-over/process")
async def process_voice_over(
    background_tasks: BackgroundTasks,
    video_url: str = Form(..., description="URL of the video to process"),
    voice_name: Optional[str] = Form(default="aria", description="Azure neural voice to use"),
    request_id: Optional[str] = Form(default=None, description="Optional request ID (auto-generated if not provided)")
):
    """
    Process video for voice over conversion using form data input.
    
    This endpoint accepts form data to handle complex URLs with special characters
    that might cause JSON parsing issues.

    Form Parameters:
        video_url (str): URL of the video to process (required)
        voice_name (str): Azure neural voice name (optional, defaults to 'aria')
        request_id (str): Optional request ID (auto-generated UUID if not provided)

    Processing Pipeline:
        1. Downloads the video from the provided URL
        2. Extracts audio from the video using MoviePy
        3. Transcribes the audio using Azure Speech Services
        4. Polishes the transcript using AI (configurable LLM provider)
        5. Generates new AI voice using Azure Speech Services
        6. Replaces the original audio with AI-generated voice
        7. Uploads the processed video to Azure Blob Storage
        8. Returns the processed video URL

    Returns:
        dict: Response with success status, message, and request ID
        
    Raises:
        HTTPException: If voice name is invalid or processing fails to start
    """
    try:
        # Generate unique request ID if not provided (same as VoiceOverRequest model)
        if not request_id:
            request_id = str(uuid.uuid4())
        
        # Validate voice name if provided
        if voice_name and voice_name not in Config.AZURE_VOICE_OPTIONS:
            available_voices = list(Config.AZURE_VOICE_OPTIONS.keys())
            raise HTTPException(
                status_code=400,
                detail=f"Invalid voice name '{voice_name}'. Available voices: {available_voices}"
            )

        # Validate video URL
        if not video_url or not video_url.strip():
            raise HTTPException(
                status_code=400,
                detail="Video URL is required and cannot be empty"
            )

        # Log the request for debugging
        print(f"🎬 Voice-over request received:")
        print(f"   Request ID: {request_id}")
        print(f"   Video URL: {video_url[:100]}...")
        print(f"   Voice: {voice_name}")

        # Add background task for processing
        background_tasks.add_task(process_voice_over_background, video_url, voice_name, request_id)

        return {
            "success": True,
            "message": "Voice-over conversion started successfully",
            "request_id": request_id,
            "video_url": video_url,
            "voice_name": voice_name,
            "status": "processing"
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, 
            detail=f"Failed to start voice-over processing: {str(e)}"
        )

@router.get("/voice-over/voices")
async def get_available_voices():
    """Get list of available Azure neural voices"""
    try:
        return {
            "voices": Config.AZURE_VOICE_OPTIONS,
            "default_voice": "aria"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get voices: {str(e)}")
