"""
Audio Processor Module for Voice Over Agent

This module handles audio extraction from video files with Azure Speech Services optimization.
Provides robust audio processing with format conversion and validation.
"""

import os
from typing import Optional
from moviepy.video.io.VideoFileClip import VideoFileClip


class AudioProcessor:
    """
    Handles audio extraction from video files with Azure Speech Services optimization.
    
    Features:
    - Audio extraction from various video formats
    - Azure Speech Services optimized output (16kHz mono WAV)
    - File validation and error handling
    - Memory-efficient processing with proper cleanup
    """
    
    def __init__(self):
        """Initialize the AudioProcessor with Azure-optimized settings."""
        # Azure Speech Services optimal settings
        self.sample_rate = 16000  # 16kHz for Azure Speech
        self.channels = 1  # Mono audio
        self.codec = 'pcm_s16le'  # 16-bit PCM for Azure Speech
    
    def extract_audio(self, video_path: str, audio_path: str) -> bool:
        """
        Extract audio from video file with Azure Speech Services optimization.
        
        Args:
            video_path (str): Path to the input video file
            audio_path (str): Path where extracted audio will be saved
            
        Returns:
            bool: True if extraction successful, False otherwise
            
        Features:
        - Converts to 16kHz mono WAV format for Azure Speech compatibility
        - Validates input video file existence and content
        - Provides detailed logging and error handling
        - Ensures proper resource cleanup
        """
        try:
            print(f"🎵 Extracting audio from: {video_path}")

            # Validate input video file
            if not self._validate_video_file(video_path):
                return False

            # Load video file
            video = VideoFileClip(video_path)

            # Check if video has audio track
            if video.audio is None:
                print("❌ Video file has no audio track")
                video.close()
                return False

            # Extract audio with Azure-optimized parameters
            audio = video.audio
            
            print(f"🔧 Converting audio to Azure Speech format (16kHz mono WAV)")
            audio.write_audiofile(
                audio_path,
                codec=self.codec,
                ffmpeg_params=[
                    '-ar', str(self.sample_rate),  # Sample rate: 16kHz
                    '-ac', str(self.channels)      # Channels: mono
                ]
            )

            # Cleanup video resources
            video.close()
            audio.close()

            # Validate extracted audio file
            if not self._validate_audio_file(audio_path):
                return False

            audio_size = os.path.getsize(audio_path)
            print(f"✅ Audio extracted successfully: {audio_path}")
            print(f"📊 Audio file size: {audio_size / (1024*1024):.2f} MB")
            return True

        except Exception as e:
            print(f"❌ Error extracting audio: {str(e)}")
            return False
    
    def _validate_video_file(self, video_path: str) -> bool:
        """
        Validate video file existence and basic properties.
        
        Args:
            video_path (str): Path to the video file
            
        Returns:
            bool: True if video file is valid, False otherwise
        """
        if not os.path.exists(video_path):
            print(f"❌ Video file not found: {video_path}")
            return False

        file_size = os.path.getsize(video_path)
        if file_size == 0:
            print(f"❌ Video file is empty: {video_path}")
            return False

        print(f"📊 Video file size: {file_size / (1024*1024):.2f} MB")
        return True
    
    def _validate_audio_file(self, audio_path: str) -> bool:
        """
        Validate extracted audio file existence and basic properties.
        
        Args:
            audio_path (str): Path to the audio file
            
        Returns:
            bool: True if audio file is valid, False otherwise
        """
        if not os.path.exists(audio_path):
            print(f"❌ Audio extraction failed - file not created: {audio_path}")
            return False

        audio_size = os.path.getsize(audio_path)
        if audio_size == 0:
            print(f"❌ Audio extraction failed - empty file: {audio_path}")
            return False

        return True