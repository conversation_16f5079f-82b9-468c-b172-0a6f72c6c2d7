"""
Transcript Processor Module for Voice Over Agent

This module handles audio transcription using Azure Speech Services and AI-powered transcript polishing
using configurable LLM providers with structured output for enhanced quality and consistency.
"""

import os
import time
from typing import Optional
import azure.cognitiveservices.speech as speechsdk
from langchain_core.messages import SystemMessage, HumanMessage

from config import Config
from llm_config.config_manager import ConfigManager
from common.common import create_llm
from Voice_Over_Agent.prompts.polish_transcript import SYSTEM_PROMPT, USER_PROMPT_TEMPLATE
from Voice_Over_Agent.state.state import PolishedTranscriptResponse


class TranscriptProcessor:
    """
    Handles audio transcription and AI-powered transcript polishing.
    
    Features:
    - Azure Speech Services integration for accurate transcription
    - Support for both single and continuous recognition modes
    - AI-powered transcript polishing using configurable LLM providers
    - Structured output with Pydantic models for reliability
    - Length-preserving transcript enhancement for audio sync
    """
    
    def __init__(self, speech_config: speechsdk.SpeechConfig):
        """
        Initialize the TranscriptProcessor with Azure Speech configuration and LLM setup.
        
        Args:
            speech_config (speechsdk.SpeechConfig): Configured Azure Speech Services client
        """
        self.speech_config = speech_config
        
        # Initialize LLM for transcript polishing using project-standard configuration
        self.config_manager = ConfigManager()
        provider = self.config_manager.provider
        self.llm = create_llm(provider, self.config_manager).get_llm()
        
        # Configuration constants
        self.max_wait_time = getattr(Config, 'VOICE_MAX_WAIT_TIME', 300)  # 5 minutes default
        self.large_file_threshold = 10 * 1024 * 1024  # 10MB threshold for continuous recognition
    
    def transcribe_audio(self, audio_path: str) -> Optional[str]:
        """
        Transcribe audio file using Azure Speech Services with automatic mode selection.
        
        Args:
            audio_path (str): Path to the audio file to transcribe
            
        Returns:
            Optional[str]: Transcribed text or None if transcription fails
            
        Features:
        - Automatic selection between single and continuous recognition
        - Robust error handling with detailed logging
        - Support for various audio formats optimized for Azure Speech
        - Configurable language settings
        """
        try:
            print("🔧 Starting Azure Speech-to-Text transcription...")

            # Validate audio file
            if not self._validate_audio_file(audio_path):
                return None

            # Configure speech recognition language
            self.speech_config.speech_recognition_language = Config.AZURE_SPEECH_STT_CONFIG["language"]

            # Create audio configuration with absolute path
            abs_audio_path = os.path.abspath(audio_path)
            print(f"🔧 Using absolute audio path: {abs_audio_path}")
            audio_config = speechsdk.audio.AudioConfig(filename=abs_audio_path)

            # Create speech recognizer
            speech_recognizer = speechsdk.SpeechRecognizer(
                speech_config=self.speech_config,
                audio_config=audio_config
            )

            # Determine recognition mode based on file size
            audio_size = os.path.getsize(audio_path)
            print(f"🔧 Audio file size: {audio_size / (1024*1024):.2f} MB")

            if audio_size < self.large_file_threshold:
                return self._single_recognition(speech_recognizer)
            else:
                return self._continuous_recognition(speech_recognizer)

        except Exception as e:
            print(f"❌ Error in Azure transcription: {str(e)}")
            print(f"❌ Exception type: {type(e).__name__}")
            return None
    
    def polish_transcript_with_ai(self, transcript: str) -> str:
        """
        Polish transcript using AI with structured output and length preservation.
        
        Args:
            transcript (str): Original transcript to polish
            
        Returns:
            str: Polished transcript with same length as input, or original on error
            
        Features:
        - Uses configurable LLM provider (Azure OpenAI, OpenAI, Anthropic, etc.)
        - Structured output with Pydantic models for reliability
        - Length preservation to maintain audio/video synchronization
        - Graceful fallback to original transcript on errors
        """
        try:
            print("🔧 Starting AI-powered transcript polishing...")
            print(f"📊 Original transcript length: {len(transcript)} characters")
            
            # Create structured LLM using Pydantic model from state.py
            structured_llm = self.llm.with_structured_output(PolishedTranscriptResponse)
            
            # Prepare prompts using project-standard prompt management
            system_prompt = SYSTEM_PROMPT
            user_prompt = USER_PROMPT_TEMPLATE.format(
                transcript=transcript,
                format_instructions="Return the polished transcript in the required JSON format."
            )
            
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=user_prompt)
            ]
            
            # Get structured response from LLM
            response = structured_llm.invoke(messages)
            polished = response.polished_transcript.strip()
            
            # Enforce strict length constraint for audio sync
            polished = self._enforce_length_constraint(polished, len(transcript))
            
            print(f"📊 Polished transcript length: {len(polished)} characters")
            print("✅ Transcript polished successfully with AI")
            return polished
            
        except Exception as e:
            print(f"❌ Error polishing transcript with AI: {str(e)}")
            print("🔄 Falling back to original transcript")
            return transcript
    
    def _single_recognition(self, speech_recognizer: speechsdk.SpeechRecognizer) -> Optional[str]:
        """
        Perform single recognition for shorter audio files.
        
        Args:
            speech_recognizer (speechsdk.SpeechRecognizer): Configured speech recognizer
            
        Returns:
            Optional[str]: Recognized text or None if recognition fails
        """
        print("🔧 Using single recognition for short audio")
        result = speech_recognizer.recognize_once()

        if result.reason == speechsdk.ResultReason.RecognizedSpeech:
            print("✅ Azure transcription completed successfully")
            print(f"📊 Transcript length: {len(result.text)} characters")
            return result.text
        elif result.reason == speechsdk.ResultReason.NoMatch:
            print("❌ No speech could be recognized")
            return None
        elif result.reason == speechsdk.ResultReason.Canceled:
            cancellation_details = result.cancellation_details
            print(f"❌ Speech recognition canceled: {cancellation_details.reason}")
            if cancellation_details.error_details:
                print(f"❌ Error details: {cancellation_details.error_details}")
            return None
    
    def _continuous_recognition(self, speech_recognizer: speechsdk.SpeechRecognizer) -> Optional[str]:
        """
        Perform continuous recognition for longer audio files.
        
        Args:
            speech_recognizer (speechsdk.SpeechRecognizer): Configured speech recognizer
            
        Returns:
            Optional[str]: Recognized text or None if recognition fails
        """
        print("🔧 Using continuous recognition for long audio")
        done = False
        transcript_parts = []

        def stop_cb(evt):
            nonlocal done
            done = True

        def recognized_cb(evt):
            if evt.result.reason == speechsdk.ResultReason.RecognizedSpeech:
                transcript_parts.append(evt.result.text)
                print(f"🔧 Recognized: {evt.result.text[:100]}...")

        # Connect callbacks
        speech_recognizer.recognized.connect(recognized_cb)
        speech_recognizer.session_stopped.connect(stop_cb)
        speech_recognizer.canceled.connect(stop_cb)

        # Start continuous recognition
        speech_recognizer.start_continuous_recognition()

        # Wait for completion with timeout
        start_time = time.time()
        while not done and (time.time() - start_time) < self.max_wait_time:
            time.sleep(0.5)

        speech_recognizer.stop_continuous_recognition()

        if not transcript_parts:
            print("❌ No speech recognized in audio")
            return None

        full_transcript = " ".join(transcript_parts)
        print("✅ Azure transcription completed successfully")
        print(f"📊 Transcript length: {len(full_transcript)} characters")
        return full_transcript
    
    def _validate_audio_file(self, audio_path: str) -> bool:
        """
        Validate audio file for transcription.
        
        Args:
            audio_path (str): Path to the audio file
            
        Returns:
            bool: True if file is valid, False otherwise
        """
        if not os.path.exists(audio_path):
            print(f"❌ Audio file not found: {audio_path}")
            return False
        
        file_size = os.path.getsize(audio_path)
        if file_size == 0:
            print(f"❌ Audio file is empty: {audio_path}")
            return False
            
        return True
    
    def _enforce_length_constraint(self, polished_text: str, target_length: int) -> str:
        """
        Enforce strict length constraint on polished text for audio synchronization.
        
        Args:
            polished_text (str): AI-polished text
            target_length (int): Target character length
            
        Returns:
            str: Text adjusted to exact target length
        """
        if len(polished_text) > target_length:
            # Truncate if too long
            polished_text = polished_text[:target_length]
            print(f"⚠️ Polished text truncated to {target_length} characters")
        elif len(polished_text) < target_length:
            # Pad with spaces if too short
            polished_text = polished_text.ljust(target_length)
            print(f"⚠️ Polished text padded to {target_length} characters")
        
        return polished_text