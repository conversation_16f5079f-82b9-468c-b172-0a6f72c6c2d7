STAGE2 PIPELINE EXECUTION LOG
====================================================================================================
Target Statement Number: 9
Attempt Number: 1
Execution Context: Enhanced Direct Pipeline - Registry Execution (All modules from registry - 90% faster)
Timestamp: 2025-08-07 19:04:47
Migration: Oracle_Postgres14
Schema: AICCTEST, Object: P_GETISSEROPOSITIVE (Procedure)
Cloud Category: Local

INITIAL INPUT:
--------------------
Original Oracle:
BEGIN

  BEGIN
    SELECT NVL(BBAG.BLOODBAGNUMBER, DD.TOKENNO)
      INTO V_BLOODNUMBER
      FROM BB.DONORMASTER DM
      LEFT OUTER JOIN BB.DONORDETAILS DD
        ON DD.DONORID = DM.DONORID
      LEFT OUTER JOIN BB.BLOODBAG BBAG
        ON DD.VISITID = BBAG.VISITID
     WHERE DM.UHID = IV_UHID
       AND DD.LOCATIONID = IV_LOCATION
       AND ROWNUM < 2
    --AND UPPER(DD.TOKENSTATUS) LIKE 'CLOSED'
     ORDER BY DM.CREATEDDATE DESC;

QMigrator Converted After Preprocessing:
BEGIN
BEGIN
SELECT NVL(BBAG.BLOODBAGNUMBER,DD.TOKENNO)
INTO V_BLOODNUMBER
FROM BB.DONORMASTER DM
LEFT OUTER JOIN BB.DONORDETAILS DD
ON DD.DONORID = DM.DONORID
LEFT OUTER JOIN BB.BLOODBAG BBAG
ON DD.VISITID = BBAG.VISITID
WHERE DM.UHID = IV_UHID
AND DD.LOCATIONID = IV_LOCATION
limit 1
comment_quad_marker_0_us
ORDER BY DM.CREATEDDATE DESC;

FEATURE EXECUTION:
--------------------

=============RESPONSIBLE PHASE ===============
========================================

1. rownum (enhanced_registry)
Path: Common/Pre/rownum.py
Changed: Yes
INPUT:
BEGIN
BEGIN
SELECT NVL(BBAG.BLOODBAGNUMBER,DD.TOKENNO)
INTO V_BLOODNUMBER
FROM BB.DONORMASTER DM
LEFT OUTER JOIN BB.DONORDETAILS DD
ON DD.DONORID = DM.DONORID
LEFT OUTER JOIN BB.BLOODBAG BBAG
ON DD.VISITID = BBAG.VISITID
WHERE DM.UHID = IV_UHID
AND DD.LOCATIONID = IV_LOCATION
limit 1
comment_quad_marker_0_us
ORDER BY DM.CREATEDDATE DESC;
OUTPUT:
begin
SELECT NVL(BBAG.BLOODBAGNUMBER,DD.TOKENNO)
INTO V_BLOODNUMBER
FROM BB.DONORMASTER DM
LEFT OUTER JOIN BB.DONORDETAILS DD
ON DD.DONORID = DM.DONORID
LEFT OUTER JOIN BB.BLOODBAG BBAG
ON DD.VISITID = BBAG.VISITID
WHERE DM.UHID = IV_UHID
AND DD.LOCATIONID = IV_LOCATION
comment_quad_marker_0_us
ORDER BY DM.CREATEDDATE DESC limit 1;
----------------------------------------

=============POST_PROCESSING PHASE ===============
========================================

2. nvl (original)
Path: Common/Statement/Pre/nvl.py
Changed: Yes
INPUT:
begin
SELECT NVL(BBAG.BLOODBAGNUMBER,DD.TOKENNO)
INTO V_BLOODNUMBER
FROM BB.DONORMASTER DM
LEFT OUTER JOIN BB.DONORDETAILS DD
ON DD.DONORID = DM.DONORID
LEFT OUTER JOIN BB.BLOODBAG BBAG
ON DD.VISITID = BBAG.VISITID
WHERE DM.UHID = IV_UHID
AND DD.LOCATIONID = IV_LOCATION
comment_quad_marker_0_us
ORDER BY DM.CREATEDDATE DESC limit 1;
OUTPUT:
begin
SELECT Coalesce(BBAG.BLOODBAGNUMBER,DD.TOKENNO)
INTO V_BLOODNUMBER
FROM BB.DONORMASTER DM
LEFT OUTER JOIN BB.DONORDETAILS DD
ON DD.DONORID = DM.DONORID
LEFT OUTER JOIN BB.BLOODBAG BBAG
ON DD.VISITID = BBAG.VISITID
WHERE DM.UHID = IV_UHID
AND DD.LOCATIONID = IV_LOCATION
comment_quad_marker_0_us
ORDER BY DM.CREATEDDATE DESC limit 1;
----------------------------------------

3. join (original)
Path: Common/Statement/Pre/join.py
Changed: No
INPUT:
begin
SELECT Coalesce(BBAG.BLOODBAGNUMBER,DD.TOKENNO)
INTO V_BLOODNUMBER
FROM BB.DONORMASTER DM
LEFT OUTER JOIN BB.DONORDETAILS DD
ON DD.DONORID = DM.DONORID
LEFT OUTER JOIN BB.BLOODBAG BBAG
ON DD.VISITID = BBAG.VISITID
WHERE DM.UHID = IV_UHID
AND DD.LOCATIONID = IV_LOCATION
comment_quad_marker_0_us
ORDER BY DM.CREATEDDATE DESC limit 1;
OUTPUT:
begin
SELECT Coalesce(BBAG.BLOODBAGNUMBER,DD.TOKENNO)
INTO V_BLOODNUMBER
FROM BB.DONORMASTER DM
LEFT OUTER JOIN BB.DONORDETAILS DD
ON DD.DONORID = DM.DONORID
LEFT OUTER JOIN BB.BLOODBAG BBAG
ON DD.VISITID = BBAG.VISITID
WHERE DM.UHID = IV_UHID
AND DD.LOCATIONID = IV_LOCATION
comment_quad_marker_0_us
ORDER BY DM.CREATEDDATE DESC limit 1;
----------------------------------------

4. exception_commenting (original)
Path: Procedure/Post/exception_commenting.py
Changed: No
INPUT:
begin
SELECT Coalesce(BBAG.BLOODBAGNUMBER,DD.TOKENNO)
INTO V_BLOODNUMBER
FROM BB.DONORMASTER DM
LEFT OUTER JOIN BB.DONORDETAILS DD
ON DD.DONORID = DM.DONORID
LEFT OUTER JOIN BB.BLOODBAG BBAG
ON DD.VISITID = BBAG.VISITID
WHERE DM.UHID = IV_UHID
AND DD.LOCATIONID = IV_LOCATION
comment_quad_marker_0_us
ORDER BY DM.CREATEDDATE DESC limit 1;
OUTPUT:
begin
SELECT Coalesce(BBAG.BLOODBAGNUMBER,DD.TOKENNO)
INTO V_BLOODNUMBER
FROM BB.DONORMASTER DM
LEFT OUTER JOIN BB.DONORDETAILS DD
ON DD.DONORID = DM.DONORID
LEFT OUTER JOIN BB.BLOODBAG BBAG
ON DD.VISITID = BBAG.VISITID
WHERE DM.UHID = IV_UHID
AND DD.LOCATIONID = IV_LOCATION
comment_quad_marker_0_us
ORDER BY DM.CREATEDDATE DESC limit 1;
----------------------------------------

5. record (enhanced_registry)
Path: Procedure/Post/record.py
Changed: No
INPUT:
begin
SELECT Coalesce(BBAG.BLOODBAGNUMBER,DD.TOKENNO)
INTO V_BLOODNUMBER
FROM BB.DONORMASTER DM
LEFT OUTER JOIN BB.DONORDETAILS DD
ON DD.DONORID = DM.DONORID
LEFT OUTER JOIN BB.BLOODBAG BBAG
ON DD.VISITID = BBAG.VISITID
WHERE DM.UHID = IV_UHID
AND DD.LOCATIONID = IV_LOCATION
comment_quad_marker_0_us
ORDER BY DM.CREATEDDATE DESC limit 1;
OUTPUT:
begin
SELECT Coalesce(BBAG.BLOODBAGNUMBER,DD.TOKENNO)
INTO V_BLOODNUMBER
FROM BB.DONORMASTER DM
LEFT OUTER JOIN BB.DONORDETAILS DD
ON DD.DONORID = DM.DONORID
LEFT OUTER JOIN BB.BLOODBAG BBAG
ON DD.VISITID = BBAG.VISITID
WHERE DM.UHID = IV_UHID
AND DD.LOCATIONID = IV_LOCATION
comment_quad_marker_0_us
ORDER BY DM.CREATEDDATE DESC limit 1;
----------------------------------------

6. strict (original)
Path: Procedure/Post/strict.py
Changed: Yes
INPUT:
begin
SELECT Coalesce(BBAG.BLOODBAGNUMBER,DD.TOKENNO)
INTO V_BLOODNUMBER
FROM BB.DONORMASTER DM
LEFT OUTER JOIN BB.DONORDETAILS DD
ON DD.DONORID = DM.DONORID
LEFT OUTER JOIN BB.BLOODBAG BBAG
ON DD.VISITID = BBAG.VISITID
WHERE DM.UHID = IV_UHID
AND DD.LOCATIONID = IV_LOCATION
comment_quad_marker_0_us
ORDER BY DM.CREATEDDATE DESC limit 1;
OUTPUT:
begin
SELECT Coalesce(BBAG.BLOODBAGNUMBER,DD.TOKENNO)
into strict V_BLOODNUMBER
FROM BB.DONORMASTER DM
LEFT OUTER JOIN BB.DONORDETAILS DD
ON DD.DONORID = DM.DONORID
LEFT OUTER JOIN BB.BLOODBAG BBAG
ON DD.VISITID = BBAG.VISITID
WHERE DM.UHID = IV_UHID
AND DD.LOCATIONID = IV_LOCATION
comment_quad_marker_0_us
ORDER BY DM.CREATEDDATE DESC limit 1;
----------------------------------------

7. sub_query_alias (enhanced_registry)
Path: Common/Post/sub_query_alias.py
Changed: Yes
INPUT:
begin
SELECT Coalesce(BBAG.BLOODBAGNUMBER,DD.TOKENNO)
into strict V_BLOODNUMBER
FROM BB.DONORMASTER DM
LEFT OUTER JOIN BB.DONORDETAILS DD
ON DD.DONORID = DM.DONORID
LEFT OUTER JOIN BB.BLOODBAG BBAG
ON DD.VISITID = BBAG.VISITID
WHERE DM.UHID = IV_UHID
AND DD.LOCATIONID = IV_LOCATION
comment_quad_marker_0_us
ORDER BY DM.CREATEDDATE DESC limit 1;
OUTPUT:
begin
SELECT Coalesce(BBAG.BLOODBAGNUMBER,DD.TOKENNO) into strict V_BLOODNUMBER
FROM BB.DONORMASTER DM
LEFT OUTER JOIN BB.DONORDETAILS DD
ON DD.DONORID = DM.DONORID
LEFT OUTER JOIN BB.BLOODBAG BBAG
ON DD.VISITID = BBAG.VISITID
WHERE DM.UHID = IV_UHID
AND DD.LOCATIONID = IV_LOCATION
comment_quad_marker_0_us
ORDER BY DM.CREATEDDATE DESC limit 1;
----------------------------------------

8. exception_variable_commenting (original)
Path: Common/Post/exception_variable_commenting.py
Changed: No
INPUT:
begin
SELECT Coalesce(BBAG.BLOODBAGNUMBER,DD.TOKENNO) into strict V_BLOODNUMBER
FROM BB.DONORMASTER DM
LEFT OUTER JOIN BB.DONORDETAILS DD
ON DD.DONORID = DM.DONORID
LEFT OUTER JOIN BB.BLOODBAG BBAG
ON DD.VISITID = BBAG.VISITID
WHERE DM.UHID = IV_UHID
AND DD.LOCATIONID = IV_LOCATION
comment_quad_marker_0_us
ORDER BY DM.CREATEDDATE DESC limit 1;
OUTPUT:
begin
SELECT Coalesce(BBAG.BLOODBAGNUMBER,DD.TOKENNO) into strict V_BLOODNUMBER
FROM BB.DONORMASTER DM
LEFT OUTER JOIN BB.DONORDETAILS DD
ON DD.DONORID = DM.DONORID
LEFT OUTER JOIN BB.BLOODBAG BBAG
ON DD.VISITID = BBAG.VISITID
WHERE DM.UHID = IV_UHID
AND DD.LOCATIONID = IV_LOCATION
comment_quad_marker_0_us
ORDER BY DM.CREATEDDATE DESC limit 1;
----------------------------------------

9. input_parameters_issue (original)
Path: Common/Post/input_parameters_issue.py
Changed: No
INPUT:
begin
SELECT Coalesce(BBAG.BLOODBAGNUMBER,DD.TOKENNO) into strict V_BLOODNUMBER
FROM BB.DONORMASTER DM
LEFT OUTER JOIN BB.DONORDETAILS DD
ON DD.DONORID = DM.DONORID
LEFT OUTER JOIN BB.BLOODBAG BBAG
ON DD.VISITID = BBAG.VISITID
WHERE DM.UHID = IV_UHID
AND DD.LOCATIONID = IV_LOCATION
comment_quad_marker_0_us
ORDER BY DM.CREATEDDATE DESC limit 1;
OUTPUT:
begin
SELECT Coalesce(BBAG.BLOODBAGNUMBER,DD.TOKENNO) into strict V_BLOODNUMBER
FROM BB.DONORMASTER DM
LEFT OUTER JOIN BB.DONORDETAILS DD
ON DD.DONORID = DM.DONORID
LEFT OUTER JOIN BB.BLOODBAG BBAG
ON DD.VISITID = BBAG.VISITID
WHERE DM.UHID = IV_UHID
AND DD.LOCATIONID = IV_LOCATION
comment_quad_marker_0_us
ORDER BY DM.CREATEDDATE DESC limit 1;
----------------------------------------

FINAL OUTPUT:
--------------------
begin
SELECT Coalesce(BBAG.BLOODBAGNUMBER,DD.TOKENNO) into strict V_BLOODNUMBER
FROM BB.DONORMASTER DM
LEFT OUTER JOIN BB.DONORDETAILS DD
ON DD.DONORID = DM.DONORID
LEFT OUTER JOIN BB.BLOODBAG BBAG
ON DD.VISITID = BBAG.VISITID
WHERE DM.UHID = IV_UHID
AND DD.LOCATIONID = IV_LOCATION
--AND UPPER(DD.TOKENSTATUS) LIKE 'CLOSED'


ORDER BY DM.CREATEDDATE DESC limit 1;

SUMMARY:
----------
Total Features: 9
Status: SUCCESS
Phase Breakdown:
  - RESPONSIBLE: 1 modules
  - POST_PROCESSING: 8 modules
