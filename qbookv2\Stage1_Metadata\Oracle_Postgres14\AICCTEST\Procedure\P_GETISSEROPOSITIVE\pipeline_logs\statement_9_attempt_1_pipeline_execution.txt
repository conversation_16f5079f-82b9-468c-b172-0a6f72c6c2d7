STAGE2 PIPELINE EXECUTION LOG
====================================================================================================
Target Statement Number: 9
Attempt Number: 1
Execution Context: Enhanced Direct Pipeline - Registry Execution (All modules from registry - 90% faster)
Timestamp: 2025-08-07 18:36:33
Migration: Oracle_Postgres14
Schema: AICCTEST, Object: P_GETISSEROPOSITIVE (Procedure)
Cloud Category: Local

EXECUTION CONTEXT EXPLANATION:
--------------------------------------------------
Unknown execution context: Enhanced Direct Pipeline - Registry Execution (All modules from registry - 90% faster)

INITIAL PIPELINE INPUT:
------------------------------
Original Oracle Statement:
BEGIN

  BEGIN
    SELECT NVL(BBAG.BLOODBAGNUMBER, DD.TOKENNO)
      INTO V_BLOODNUMBER
      FROM BB.DONORMASTER DM
      LEFT OUTER JOIN BB.DONORDETAILS DD
        ON DD.DONORID = DM.DONORID
      LEFT OUTER JOIN BB.BLOODBAG BBAG
        ON DD.VISITID = BBAG.VISITID
     WHERE DM.UHID = IV_UHID
       AND DD.LOCATIONID = IV_LOCATION
       AND ROWNUM < 2
    --AND UPPER(DD.TOKENSTATUS) LIKE 'CLOSED'
     ORDER BY DM.CREATEDDATE DESC;

QMigrator Converted Statement (After Typecasting):
BEGIN
BEGIN
SELECT NVL(BBAG.BLOODBAGNUMBER,DD.TOKENNO)
INTO V_BLOODNUMBER
FROM BB.DONORMASTER DM
LEFT OUTER JOIN BB.DONORDETAILS DD
ON DD.DONORID = DM.DONORID
LEFT OUTER JOIN BB.BLOODBAG BBAG
ON DD.VISITID = BBAG.VISITID
WHERE DM.UHID = IV_UHID
AND DD.LOCATIONID = IV_LOCATION
limit 1
comment_quad_marker_0_us
ORDER BY DM.CREATEDDATE DESC;

PIPELINE EXECUTION DETAILS:
----------------------------------------

>>> RESPONSIBLE EXECUTION PHASE <<<
======================================================================

Feature 1: rownum
Execution Phase: RESPONSIBLE
Module Source Type: enhanced_registry
Module Relative Path: Common/Pre/rownum.py
Module Full Path: C:\Repos\New_Repos\Web-Agents/qbookv2\Stage1_Metadata\Oracle_Postgres14\Enhanced_Modules_Registry\Common/Pre/rownum.py
Input Statement Length: 339 characters
Output Statement Length: 333 characters
Transformation Applied: Yes
Feature Input Statement:
BEGIN
BEGIN
SELECT NVL(BBAG.BLOODBAGNUMBER,DD.TOKENNO)
INTO V_BLOODNUMBER
FROM BB.DONORMASTER DM
LEFT OUTER JOIN BB.DONORDETAILS DD
ON DD.DONORID = DM.DONORID
LEFT OUTER JOIN BB.BLOODBAG BBAG
ON DD.VISITID = BBAG.VISITID
WHERE DM.UHID = IV_UHID
AND DD.LOCATIONID = IV_LOCATION
limit 1
comment_quad_marker_0_us
ORDER BY DM.CREATEDDATE DESC;

Feature Output Statement:
begin
SELECT NVL(BBAG.BLOODBAGNUMBER,DD.TOKENNO)
INTO V_BLOODNUMBER
FROM BB.DONORMASTER DM
LEFT OUTER JOIN BB.DONORDETAILS DD
ON DD.DONORID = DM.DONORID
LEFT OUTER JOIN BB.BLOODBAG BBAG
ON DD.VISITID = BBAG.VISITID
WHERE DM.UHID = IV_UHID
AND DD.LOCATIONID = IV_LOCATION
comment_quad_marker_0_us
ORDER BY DM.CREATEDDATE DESC limit 1;
--------------------------------------------------------------------------------

>>> POST_PROCESSING EXECUTION PHASE <<<
======================================================================

Feature 2: nvl
Execution Phase: POST_PROCESSING
Module Source Type: original
Module Relative Path: Common/Statement/Pre/nvl.py
Module Full Path: C:\Repos\New_Repos\Web-Agents/qbookv2\Conversion_Modules\Oracle_Postgres14\Common/Statement/Pre/nvl.py
Input Statement Length: 333 characters
Output Statement Length: 338 characters
Transformation Applied: Yes
Feature Input Statement:
begin
SELECT NVL(BBAG.BLOODBAGNUMBER,DD.TOKENNO)
INTO V_BLOODNUMBER
FROM BB.DONORMASTER DM
LEFT OUTER JOIN BB.DONORDETAILS DD
ON DD.DONORID = DM.DONORID
LEFT OUTER JOIN BB.BLOODBAG BBAG
ON DD.VISITID = BBAG.VISITID
WHERE DM.UHID = IV_UHID
AND DD.LOCATIONID = IV_LOCATION
comment_quad_marker_0_us
ORDER BY DM.CREATEDDATE DESC limit 1;

Feature Output Statement:
begin
SELECT Coalesce(BBAG.BLOODBAGNUMBER,DD.TOKENNO)
INTO V_BLOODNUMBER
FROM BB.DONORMASTER DM
LEFT OUTER JOIN BB.DONORDETAILS DD
ON DD.DONORID = DM.DONORID
LEFT OUTER JOIN BB.BLOODBAG BBAG
ON DD.VISITID = BBAG.VISITID
WHERE DM.UHID = IV_UHID
AND DD.LOCATIONID = IV_LOCATION
comment_quad_marker_0_us
ORDER BY DM.CREATEDDATE DESC limit 1;
--------------------------------------------------------------------------------

Feature 3: join
Execution Phase: POST_PROCESSING
Module Source Type: original
Module Relative Path: Common/Statement/Pre/join.py
Module Full Path: C:\Repos\New_Repos\Web-Agents/qbookv2\Conversion_Modules\Oracle_Postgres14\Common/Statement/Pre/join.py
Input Statement Length: 338 characters
Output Statement Length: 338 characters
Transformation Applied: No
Feature Input Statement:
begin
SELECT Coalesce(BBAG.BLOODBAGNUMBER,DD.TOKENNO)
INTO V_BLOODNUMBER
FROM BB.DONORMASTER DM
LEFT OUTER JOIN BB.DONORDETAILS DD
ON DD.DONORID = DM.DONORID
LEFT OUTER JOIN BB.BLOODBAG BBAG
ON DD.VISITID = BBAG.VISITID
WHERE DM.UHID = IV_UHID
AND DD.LOCATIONID = IV_LOCATION
comment_quad_marker_0_us
ORDER BY DM.CREATEDDATE DESC limit 1;

Feature Output Statement:
begin
SELECT Coalesce(BBAG.BLOODBAGNUMBER,DD.TOKENNO)
INTO V_BLOODNUMBER
FROM BB.DONORMASTER DM
LEFT OUTER JOIN BB.DONORDETAILS DD
ON DD.DONORID = DM.DONORID
LEFT OUTER JOIN BB.BLOODBAG BBAG
ON DD.VISITID = BBAG.VISITID
WHERE DM.UHID = IV_UHID
AND DD.LOCATIONID = IV_LOCATION
comment_quad_marker_0_us
ORDER BY DM.CREATEDDATE DESC limit 1;
--------------------------------------------------------------------------------

Feature 4: exception_commenting
Execution Phase: POST_PROCESSING
Module Source Type: original
Module Relative Path: Procedure/Post/exception_commenting.py
Module Full Path: C:\Repos\New_Repos\Web-Agents/qbookv2\Conversion_Modules\Oracle_Postgres14\Procedure/Post/exception_commenting.py
Input Statement Length: 338 characters
Output Statement Length: 338 characters
Transformation Applied: No
Feature Input Statement:
begin
SELECT Coalesce(BBAG.BLOODBAGNUMBER,DD.TOKENNO)
INTO V_BLOODNUMBER
FROM BB.DONORMASTER DM
LEFT OUTER JOIN BB.DONORDETAILS DD
ON DD.DONORID = DM.DONORID
LEFT OUTER JOIN BB.BLOODBAG BBAG
ON DD.VISITID = BBAG.VISITID
WHERE DM.UHID = IV_UHID
AND DD.LOCATIONID = IV_LOCATION
comment_quad_marker_0_us
ORDER BY DM.CREATEDDATE DESC limit 1;

Feature Output Statement:
begin
SELECT Coalesce(BBAG.BLOODBAGNUMBER,DD.TOKENNO)
INTO V_BLOODNUMBER
FROM BB.DONORMASTER DM
LEFT OUTER JOIN BB.DONORDETAILS DD
ON DD.DONORID = DM.DONORID
LEFT OUTER JOIN BB.BLOODBAG BBAG
ON DD.VISITID = BBAG.VISITID
WHERE DM.UHID = IV_UHID
AND DD.LOCATIONID = IV_LOCATION
comment_quad_marker_0_us
ORDER BY DM.CREATEDDATE DESC limit 1;
--------------------------------------------------------------------------------

Feature 5: record
Execution Phase: POST_PROCESSING
Module Source Type: enhanced_registry
Module Relative Path: Procedure/Post/record.py
Module Full Path: C:\Repos\New_Repos\Web-Agents/qbookv2\Stage1_Metadata\Oracle_Postgres14\Enhanced_Modules_Registry\Procedure/Post/record.py
Input Statement Length: 338 characters
Output Statement Length: 338 characters
Transformation Applied: No
Feature Input Statement:
begin
SELECT Coalesce(BBAG.BLOODBAGNUMBER,DD.TOKENNO)
INTO V_BLOODNUMBER
FROM BB.DONORMASTER DM
LEFT OUTER JOIN BB.DONORDETAILS DD
ON DD.DONORID = DM.DONORID
LEFT OUTER JOIN BB.BLOODBAG BBAG
ON DD.VISITID = BBAG.VISITID
WHERE DM.UHID = IV_UHID
AND DD.LOCATIONID = IV_LOCATION
comment_quad_marker_0_us
ORDER BY DM.CREATEDDATE DESC limit 1;

Feature Output Statement:
begin
SELECT Coalesce(BBAG.BLOODBAGNUMBER,DD.TOKENNO)
INTO V_BLOODNUMBER
FROM BB.DONORMASTER DM
LEFT OUTER JOIN BB.DONORDETAILS DD
ON DD.DONORID = DM.DONORID
LEFT OUTER JOIN BB.BLOODBAG BBAG
ON DD.VISITID = BBAG.VISITID
WHERE DM.UHID = IV_UHID
AND DD.LOCATIONID = IV_LOCATION
comment_quad_marker_0_us
ORDER BY DM.CREATEDDATE DESC limit 1;
--------------------------------------------------------------------------------

Feature 6: strict
Execution Phase: POST_PROCESSING
Module Source Type: original
Module Relative Path: Procedure/Post/strict.py
Module Full Path: C:\Repos\New_Repos\Web-Agents/qbookv2\Conversion_Modules\Oracle_Postgres14\Procedure/Post/strict.py
Input Statement Length: 338 characters
Output Statement Length: 345 characters
Transformation Applied: Yes
Feature Input Statement:
begin
SELECT Coalesce(BBAG.BLOODBAGNUMBER,DD.TOKENNO)
INTO V_BLOODNUMBER
FROM BB.DONORMASTER DM
LEFT OUTER JOIN BB.DONORDETAILS DD
ON DD.DONORID = DM.DONORID
LEFT OUTER JOIN BB.BLOODBAG BBAG
ON DD.VISITID = BBAG.VISITID
WHERE DM.UHID = IV_UHID
AND DD.LOCATIONID = IV_LOCATION
comment_quad_marker_0_us
ORDER BY DM.CREATEDDATE DESC limit 1;

Feature Output Statement:
begin
SELECT Coalesce(BBAG.BLOODBAGNUMBER,DD.TOKENNO)
into strict V_BLOODNUMBER
FROM BB.DONORMASTER DM
LEFT OUTER JOIN BB.DONORDETAILS DD
ON DD.DONORID = DM.DONORID
LEFT OUTER JOIN BB.BLOODBAG BBAG
ON DD.VISITID = BBAG.VISITID
WHERE DM.UHID = IV_UHID
AND DD.LOCATIONID = IV_LOCATION
comment_quad_marker_0_us
ORDER BY DM.CREATEDDATE DESC limit 1;
--------------------------------------------------------------------------------

Feature 7: sub_query_alias
Execution Phase: POST_PROCESSING
Module Source Type: enhanced_registry
Module Relative Path: Common/Post/sub_query_alias.py
Module Full Path: C:\Repos\New_Repos\Web-Agents/qbookv2\Stage1_Metadata\Oracle_Postgres14\Enhanced_Modules_Registry\Common/Post/sub_query_alias.py
Input Statement Length: 345 characters
Output Statement Length: 345 characters
Transformation Applied: Yes
Feature Input Statement:
begin
SELECT Coalesce(BBAG.BLOODBAGNUMBER,DD.TOKENNO)
into strict V_BLOODNUMBER
FROM BB.DONORMASTER DM
LEFT OUTER JOIN BB.DONORDETAILS DD
ON DD.DONORID = DM.DONORID
LEFT OUTER JOIN BB.BLOODBAG BBAG
ON DD.VISITID = BBAG.VISITID
WHERE DM.UHID = IV_UHID
AND DD.LOCATIONID = IV_LOCATION
comment_quad_marker_0_us
ORDER BY DM.CREATEDDATE DESC limit 1;

Feature Output Statement:
begin
SELECT Coalesce(BBAG.BLOODBAGNUMBER,DD.TOKENNO) into strict V_BLOODNUMBER
FROM BB.DONORMASTER DM
LEFT OUTER JOIN BB.DONORDETAILS DD
ON DD.DONORID = DM.DONORID
LEFT OUTER JOIN BB.BLOODBAG BBAG
ON DD.VISITID = BBAG.VISITID
WHERE DM.UHID = IV_UHID
AND DD.LOCATIONID = IV_LOCATION
comment_quad_marker_0_us
ORDER BY DM.CREATEDDATE DESC limit 1;
--------------------------------------------------------------------------------

Feature 8: exception_variable_commenting
Execution Phase: POST_PROCESSING
Module Source Type: original
Module Relative Path: Common/Post/exception_variable_commenting.py
Module Full Path: C:\Repos\New_Repos\Web-Agents/qbookv2\Conversion_Modules\Oracle_Postgres14\Common/Post/exception_variable_commenting.py
Input Statement Length: 345 characters
Output Statement Length: 345 characters
Transformation Applied: No
Feature Input Statement:
begin
SELECT Coalesce(BBAG.BLOODBAGNUMBER,DD.TOKENNO) into strict V_BLOODNUMBER
FROM BB.DONORMASTER DM
LEFT OUTER JOIN BB.DONORDETAILS DD
ON DD.DONORID = DM.DONORID
LEFT OUTER JOIN BB.BLOODBAG BBAG
ON DD.VISITID = BBAG.VISITID
WHERE DM.UHID = IV_UHID
AND DD.LOCATIONID = IV_LOCATION
comment_quad_marker_0_us
ORDER BY DM.CREATEDDATE DESC limit 1;

Feature Output Statement:
begin
SELECT Coalesce(BBAG.BLOODBAGNUMBER,DD.TOKENNO) into strict V_BLOODNUMBER
FROM BB.DONORMASTER DM
LEFT OUTER JOIN BB.DONORDETAILS DD
ON DD.DONORID = DM.DONORID
LEFT OUTER JOIN BB.BLOODBAG BBAG
ON DD.VISITID = BBAG.VISITID
WHERE DM.UHID = IV_UHID
AND DD.LOCATIONID = IV_LOCATION
comment_quad_marker_0_us
ORDER BY DM.CREATEDDATE DESC limit 1;
--------------------------------------------------------------------------------

Feature 9: input_parameters_issue
Execution Phase: POST_PROCESSING
Module Source Type: original
Module Relative Path: Common/Post/input_parameters_issue.py
Module Full Path: C:\Repos\New_Repos\Web-Agents/qbookv2\Conversion_Modules\Oracle_Postgres14\Common/Post/input_parameters_issue.py
Input Statement Length: 345 characters
Output Statement Length: 345 characters
Transformation Applied: No
Feature Input Statement:
begin
SELECT Coalesce(BBAG.BLOODBAGNUMBER,DD.TOKENNO) into strict V_BLOODNUMBER
FROM BB.DONORMASTER DM
LEFT OUTER JOIN BB.DONORDETAILS DD
ON DD.DONORID = DM.DONORID
LEFT OUTER JOIN BB.BLOODBAG BBAG
ON DD.VISITID = BBAG.VISITID
WHERE DM.UHID = IV_UHID
AND DD.LOCATIONID = IV_LOCATION
comment_quad_marker_0_us
ORDER BY DM.CREATEDDATE DESC limit 1;

Feature Output Statement:
begin
SELECT Coalesce(BBAG.BLOODBAGNUMBER,DD.TOKENNO) into strict V_BLOODNUMBER
FROM BB.DONORMASTER DM
LEFT OUTER JOIN BB.DONORDETAILS DD
ON DD.DONORID = DM.DONORID
LEFT OUTER JOIN BB.BLOODBAG BBAG
ON DD.VISITID = BBAG.VISITID
WHERE DM.UHID = IV_UHID
AND DD.LOCATIONID = IV_LOCATION
comment_quad_marker_0_us
ORDER BY DM.CREATEDDATE DESC limit 1;
--------------------------------------------------------------------------------

FINAL PIPELINE OUTPUT:
------------------------------
begin
SELECT Coalesce(BBAG.BLOODBAGNUMBER,DD.TOKENNO) into strict V_BLOODNUMBER
FROM BB.DONORMASTER DM
LEFT OUTER JOIN BB.DONORDETAILS DD
ON DD.DONORID = DM.DONORID
LEFT OUTER JOIN BB.BLOODBAG BBAG
ON DD.VISITID = BBAG.VISITID
WHERE DM.UHID = IV_UHID
AND DD.LOCATIONID = IV_LOCATION
--AND UPPER(DD.TOKENSTATUS) LIKE 'CLOSED'


ORDER BY DM.CREATEDDATE DESC limit 1;

PIPELINE EXECUTION SUMMARY:
-----------------------------------
Total Features Executed: 9
Pipeline Execution Status: SUCCESS

Module Source Type Breakdown:
  - enhanced_registry: 3 modules
  - original: 6 modules

Execution Phase Breakdown:
  - RESPONSIBLE: 1 modules
  - POST_PROCESSING: 8 modules

Pipeline Execution Log Location: C:\Repos\New_Repos\Web-Agents/qbookv2\Stage1_Metadata\Oracle_Postgres14\AICCTEST\Procedure\P_GETISSEROPOSITIVE\pipeline_logs\statement_9_attempt_1_pipeline_execution.txt
