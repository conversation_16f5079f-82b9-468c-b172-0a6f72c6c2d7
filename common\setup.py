from config import Config
import os
import shutil
from common.api import api_authentication, decrypt_database_details


class Pre_SetupManager():  
    
    def __init__(self, Config: Config, request):
        self.config = Config
        self.request = request
        self.conversion_path = None
        self.target_db_credentials = None
        self.project_DB_details = None
        self.tgt_object_id = None
        self.pre_setup_migration()
    
    def pre_setup_migration(self):
        print("Setting up the environment with the provided configuration...")
        print(f"Using connection URL: {self.config.Connection_URL}")
        
        # setup Migration Name
        mig_name =  self.request.migration_name
        print(f"Migration Name: {mig_name}")
    
        
        Source_DB = mig_name.split('_')[0]
        os.environ['SOURCE_DATABASE'] = Source_DB
        print(f"Setting Source Database: {Source_DB}")
        Target_DB = mig_name.split('_')[1]
        os.environ['TARGET_DATABASE'] = Target_DB
        print(f"Setting Target Database: {Target_DB}")

        # Setup Conversion Directories
        self.setup_conversion_directories()
        
        # get target connection details from the api.
        token_data = api_authentication(self.request.project_id)
        self.project_DB_details = decrypt_database_details(token_data, self.request.project_id, 'Project', '')
        # print(f"Project DB Details: {project_DB_details}")
        
        dr_db_details = decrypt_database_details(token_data, self.request.project_id, 'Target', str(self.request.dr_connection_id))
        # print(f"DR DB Details: {dr_db_details}")
        print(f"✅ DR DB credentials set from the API")
        self.dr_db_credentials = dr_db_details
        if self.request.target_connection_id and self.request.target_connection_id != '':
            target_DB_details = decrypt_database_details(token_data, self.request.project_id, 'Target', str(self.request.target_connection_id))
            # print(f"Target DB Details: {target_DB_details}")

            # Store target DB details as credentials for workflow use
            self.target_db_credentials = target_DB_details
            print(f"✅ Target DB credentials set from the API")
        self.tgt_object_id = self.request.tgt_object_id
        
        

    def setup_conversion_directories(self):
        """
        Create conversion directories based on request parameters.

        Creates directory structure: PRJ{project_id}SRC/Conversion_Agent/{tgt_object_id}_{objectname}/
        - For local: creates in main.py directory
        - For cloud: creates under /mnt/pypod mount path
        """
        try:
            # Extract parameters from request
            project_id = self.request.project_id
            tgt_object_id = self.request.tgt_object_id
            objectname = self.request.objectname
            cloud_category = self.request.cloud_category.lower()

            # Build directory structure
            folder_structure = f"PRJ{project_id}SRC/Conversion_Agent/{tgt_object_id}_{objectname}"

            # Determine base path based on cloud category
            if cloud_category == 'cloud':
                base_path = self.config.Cloud_Path  # /mnt/pypod
                conversion_path = os.path.join(base_path, folder_structure)
                print(f"🌥️ Using cloud path: {conversion_path}")
            else:
                base_path = self.config.Local_Path  # main.py directory
                conversion_path = os.path.join(base_path, folder_structure)
                print(f"💻 Using local path: {conversion_path}")

            # Delete only the specific {tgt_object_id}_{objectname} folder if it exists, then create it fresh
            if os.path.exists(conversion_path):
                shutil.rmtree(conversion_path)
            os.makedirs(conversion_path)
            print(f"📁 Created conversion directories: {conversion_path}")

            # Store path in setup_manager for workflow state access
            self.conversion_path = conversion_path
            print(f"✅ Conversion path set for workflow state: {conversion_path}")

        except Exception as e:
            print(f"❌ Failed to create conversion directories: {str(e)}")
            raise Exception(f"Failed to create conversion directories: {str(e)}")