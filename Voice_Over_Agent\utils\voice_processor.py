"""
Voice Over Agent Main Processor

This module orchestrates the complete voice-over conversion workflow by coordinating
specialized components for video processing, audio extraction, transcription, AI polishing,
speech synthesis, and final video composition.

Architecture:
- Modular design with specialized components for each processing step
- Provider-agnostic LLM integration for transcript polishing
- Azure Services integration for speech and storage operations
- Comprehensive error handling and logging throughout the pipeline
- Structured output using Pydantic models for reliability
"""

import os
import time
import uuid
from typing import Optional
import azure.cognitiveservices.speech as speechsdk

# Import configuration and state models
from config import Config
from Voice_Over_Agent.state.state import VoiceOverResponse

# Import specialized processing components
from Voice_Over_Agent.utils.video_downloader import VideoDownloader
from Voice_Over_Agent.utils.audio_processor import AudioProcessor
from Voice_Over_Agent.utils.transcript_processor import TranscriptProcessor
from Voice_Over_Agent.utils.speech_synthesizer import SpeechSynthesizer
from Voice_Over_Agent.utils.video_composer import VideoComposer
from Voice_Over_Agent.utils.azure_storage_manager import AzureStorageManager


def process_voice_over_request(video_url: str, voice_name: Optional[str] = None) -> VoiceOverResponse:
    """
    Main entry point for voice-over conversion requests.
    
    Orchestrates the complete workflow from video URL to final processed video with AI voice.
    
    Args:
        video_url (str): URL of the video to process
        voice_name (Optional[str]): Azure neural voice name to use for synthesis
        
    Returns:
        VoiceOverResponse: Structured response with success status, output URL, and processing metrics
        
    Workflow:
        1. Initialize AzureVoiceConverter with all required services
        2. Process video through complete pipeline
        3. Return structured response with results and timing
    """
    start_time = time.time()
    
    try:
        print("🚀 Starting voice-over conversion request...")
        print(f"📹 Video URL: {video_url}")
        print(f"🎤 Voice: {voice_name or 'default'}")
        
        # Initialize converter with all specialized components
        converter = AzureVoiceConverter()
        
        # Process video through complete pipeline
        result_url = converter.process_video(video_url, voice_name)
        
        processing_time = time.time() - start_time
        print(f"⏱️ Total processing time: {processing_time:.2f} seconds")
        
        if result_url:
            return VoiceOverResponse(
                success=True,
                output_url=result_url,
                processing_time=processing_time
            )
        else:
            return VoiceOverResponse(
                success=False,
                error_message="Voice-over conversion failed. Check logs for details.",
                processing_time=processing_time
            )
            
    except Exception as e:
        processing_time = time.time() - start_time
        print(f"❌ Unexpected error in voice-over request: {str(e)}")
        return VoiceOverResponse(
            success=False,
            error_message=f"Unexpected error: {str(e)}",
            processing_time=processing_time
        )


class AzureVoiceConverter:
    """
    Main orchestrator for the voice-over conversion pipeline.
    
    Coordinates specialized components to transform videos with AI-generated voice-overs:
    - Video downloading and validation
    - Audio extraction and processing
    - Speech-to-text transcription
    - AI-powered transcript polishing
    - Neural voice synthesis
    - Video composition and encoding
    - Azure Storage management
    
    Features:
    - Modular architecture with specialized components
    - Comprehensive error handling and recovery
    - Detailed logging and progress tracking
    - Automatic cleanup of temporary files
    - Azure Services integration throughout
    """
    
    def __init__(self):
        """
        Initialize the AzureVoiceConverter with all required services and components.
        
        Sets up:
        - Azure Speech Services configuration
        - Azure Blob Storage management
        - Specialized processing components
        - Directory structure for temporary files
        """
        print("🔧 Initializing Azure Voice Converter...")
        
        # Setup Azure services and directory structure
        self.setup_azure_services()
        self.create_directories()
        
        # Initialize specialized processing components
        self.video_downloader = VideoDownloader()
        self.audio_processor = AudioProcessor()
        self.transcript_processor = TranscriptProcessor(self.speech_config)
        self.speech_synthesizer = SpeechSynthesizer(self.speech_config)
        self.video_composer = VideoComposer()
        self.storage_manager = AzureStorageManager()
        
        print("✅ Azure Voice Converter initialized successfully")
    
    def setup_azure_services(self) -> None:
        """
        Setup and validate Azure Speech Services configuration.
        
        Configures:
        - Azure Speech Services client with credentials validation
        - Connection testing to ensure services are accessible
        - Error handling for configuration issues
        
        Raises:
            ValueError: If Azure credentials are not properly configured
        """
        try:
            # Validate Azure Speech credentials
            if not Config.AZURE_SPEECH_KEY:
                raise ValueError("Azure Speech key not configured. Please update config.py")

            if not Config.AZURE_SPEECH_REGION:
                raise ValueError("Azure Speech region not configured. Please update config.py")

            print(f"🔧 Configuring Azure Speech Services in region: {Config.AZURE_SPEECH_REGION}")

            # Initialize Azure Speech Service client
            self.speech_config = speechsdk.SpeechConfig(
                subscription=Config.AZURE_SPEECH_KEY,
                region=Config.AZURE_SPEECH_REGION
            )

            # Test Azure Speech Services connection
            self._test_azure_speech_connection()
            
            print("✅ Azure Speech Services configured successfully")

        except Exception as e:
            print(f"❌ Error setting up Azure services: {str(e)}")
            print("Please verify your Azure credentials in config.py")
            raise
    
    def create_directories(self) -> None:
        """
        Create necessary directories for temporary and output files.
        
        Creates:
        - Temporary folder for intermediate processing files
        - Output folder for final processed videos
        """
        directories = [Config.VOICE_OUTPUT_FOLDER, Config.VOICE_TEMP_FOLDER]
        for directory in directories:
            if not os.path.exists(directory):
                os.makedirs(directory)
                print(f"📁 Created directory: {directory}")
    
    def process_video(self, video_url: str, voice_name: Optional[str] = None) -> Optional[str]:
        """
        Process video through complete voice-over conversion pipeline.
        
        Args:
            video_url (str): URL of the video to process
            voice_name (Optional[str]): Azure neural voice name for synthesis
            
        Returns:
            Optional[str]: URL of processed video or None if processing fails
            
        Pipeline Steps:
            1. Download video from URL
            2. Extract audio from video
            3. Transcribe audio to text using Azure Speech Services
            4. Polish transcript using AI (configurable LLM provider)
            5. Generate AI voice from polished transcript
            6. Replace original audio with AI voice in video
            7. Upload final video to Azure Blob Storage
            8. Cleanup temporary files
        """
        # Generate unique identifiers for this processing session
        timestamp = int(time.time())
        unique_id = str(uuid.uuid4())[:8]
        session_id = f"{timestamp}_{unique_id}"
        
        # Define file paths for processing pipeline
        file_paths = self._generate_file_paths(session_id)
        
        try:
            print(f"🔧 Starting video processing pipeline (Session: {session_id})")
            
            # Step 1: Download video
            print("📥 Step 1: Downloading video...")
            if not self.video_downloader.download_video(video_url, file_paths['video']):
                return None

            # Step 2: Extract audio
            print("🎵 Step 2: Extracting audio...")
            if not self.audio_processor.extract_audio(file_paths['video'], file_paths['audio']):
                self.cleanup_temp_files(file_paths['video'])
                return None

            # Step 3: Transcribe audio
            print("📝 Step 3: Transcribing audio...")
            transcript = self.transcript_processor.transcribe_audio(file_paths['audio'])
            if not transcript:
                self.cleanup_temp_files(file_paths['video'], file_paths['audio'])
                return None

            print(f"📄 Original transcript: {transcript[:100]}...")

            # Step 4: Polish transcript with AI
            print("✨ Step 4: Polishing transcript with AI...")
            polished_transcript = self.transcript_processor.polish_transcript_with_ai(transcript)
            print(f"📄 Polished transcript: {polished_transcript[:100]}...")

            # Step 5: Generate AI voice
            print("🎤 Step 5: Generating AI voice...")
            if not self.speech_synthesizer.generate_ai_voice(
                polished_transcript, file_paths['ai_audio'], voice_name
            ):
                self.cleanup_temp_files(file_paths['video'], file_paths['audio'])
                return None

            # Step 6: Compose final video
            print("🎬 Step 6: Composing final video...")
            if not self.video_composer.replace_audio_in_video(
                file_paths['video'], file_paths['ai_audio'], file_paths['output']
            ):
                self.cleanup_temp_files(file_paths['video'], file_paths['audio'], file_paths['ai_audio'])
                return None

            # Step 7: Upload to Azure Storage
            print("☁️ Step 7: Uploading to Azure Storage...")
            blob_url = self.storage_manager.upload_output_video(
                file_paths['output'], file_paths['output_filename']
            )

            # Step 8: Cleanup all files (including output file after successful upload)
            print("🧹 Step 8: Cleaning up all temporary and output files...")
            if blob_url:
                # If upload was successful, clean up everything including output file
                self.cleanup_temp_files(
                    file_paths['video'], 
                    file_paths['audio'], 
                    file_paths['ai_audio'],
                    file_paths['output']  # Also remove output file since it's now in blob storage
                )
                print("🗑️ Output file removed after successful upload to Azure Blob Storage")
            else:
                # If upload failed, keep output file locally but clean up temp files
                self.cleanup_temp_files(
                    file_paths['video'], 
                    file_paths['audio'], 
                    file_paths['ai_audio']
                )
                print("💾 Output file kept locally since blob upload failed")

            print("🎉 Voice-over conversion completed successfully!")
            return blob_url or file_paths['output']

        except Exception as e:
            print(f"❌ Error in video processing pipeline: {str(e)}")
            # Cleanup any created files on error
            self.cleanup_temp_files(*file_paths.values())
            return None
    
    def cleanup_temp_files(self, *file_paths: str) -> None:
        """
        Clean up temporary files created during processing.
        
        Args:
            *file_paths: Variable number of file paths to clean up
            
        Features:
        - Safe deletion with error handling
        - Detailed logging of cleanup operations
        - Continues cleanup even if individual files fail to delete
        """
        for file_path in file_paths:
            if file_path and os.path.exists(file_path):
                try:
                    os.remove(file_path)
                    print(f"🗑️ Cleaned up: {os.path.basename(file_path)}")
                except Exception as e:
                    print(f"⚠️ Could not clean up {file_path}: {str(e)}")
    
    def _generate_file_paths(self, session_id: str) -> dict:
        """
        Generate file paths for processing pipeline.
        
        Args:
            session_id (str): Unique session identifier
            
        Returns:
            dict: Dictionary containing all file paths for the session
        """
        return {
            'video': os.path.join(Config.VOICE_TEMP_FOLDER, f"video_{session_id}.mp4"),
            'audio': os.path.join(Config.VOICE_TEMP_FOLDER, f"audio_{session_id}.wav"),
            'ai_audio': os.path.join(Config.VOICE_TEMP_FOLDER, f"ai_audio_{session_id}.mp3"),
            'output': os.path.join(Config.VOICE_OUTPUT_FOLDER, f"voice_over_{session_id}.mp4"),
            'output_filename': f"voice_over_{session_id}.mp4"
        }
    
    def _test_azure_speech_connection(self) -> None:
        """
        Test Azure Speech Services connection to validate credentials.
        
        Raises:
            Exception: If connection test fails
        """
        print("🔍 Testing Azure Speech Services connection...")
        try:
            # Simple test to validate credentials
            test_config = speechsdk.SpeechConfig(
                subscription=Config.AZURE_SPEECH_KEY,
                region=Config.AZURE_SPEECH_REGION
            )
            test_config.speech_recognition_language = "en-US"
            print("✅ Azure Speech Services connection validated")
        except Exception as speech_error:
            print(f"❌ Azure Speech Services validation failed: {speech_error}")
            raise