import requests

# Define variables as they were originally used
original_source_code = """
CREATE OR REPLACE  PROCEDURE "AICCTEST"."P_GETISSEROPOSITIVE" (IV_UHID     IN VARCHAR2,
                                                   IV_LOCATION IN VARCHAR2,
                                                   OV_RESULT   OUT CHARACTER) AS
  V_BLOODNUMBER      VARCHAR2(4000);
  V_LRN              NUMBER;
  V_ISSEROTESTSID    NUMBER;
  V_COUNT            NUMBER;
  V_ISSEROTESTVALUES VARCHAR2(4000);
BEGIN

  BEGIN
    SELECT NVL(BBAG.BLOODBAGNUMBER, DD.TOKENNO)
      INTO V_BLOODNUMBER
      FROM BB.DONORMASTER DM
      LEFT OUTER JOIN BB.DONORDETAILS DD
        ON DD.DONORID = DM.DONORID
      LEFT OUTER JOIN BB.BLOODBAG BBAG
        ON DD.VISITID = BBAG.VISITID
     WHERE DM.UHID = IV_UHID
       AND DD.LOCATIONID = IV_LOCATION
       AND ROWNUM < 2
    --AND UPPER(DD.TOKENSTATUS) LIKE 'CLOSED'
     ORDER BY DM.CREATEDDATE DESC;

  EXCEPTION
    WHEN NO_DATA_FOUND THEN
      V_BLOODNUMBER := NULL;

  END;

  IF V_BLOODNUMBER IS NULL THEN

    OV_RESULT := 'N';

  ELSE

    SELECT RR.LRN
      INTO V_LRN
      FROM LAB.RAISEREQUEST RR
     WHERE RR.PATIENTSERVICENO = V_BLOODNUMBER
       AND RR.PATIENTSERVICE = 'BLOODBANK'
       AND RR.LOCATIONID = IV_LOCATION
       AND ROWNUM < 2
     ORDER BY RR.CREATEDDATE DESC;

    FOR I IN (SELECT EXTRACTVALUE(XT.COLUMN_VALUE, 'E') AS SEROTESTIDS
                FROM TABLE(XMLSEQUENCE(EXTRACT(XMLTYPE(REPLACE(('<X><E>' ||
                                                               (SELECT BCNF.CONFIGVALUE
                                                                   FROM BB.BBCONFIG BCNF
                                                                  WHERE UPPER(BCNF.CONFIG_KEY) =
                                                                        'ISSEROPOSITIVETESTS') ||
                                                               '</E></X>'),
                                                               ',',
                                                               '</E><E>')),
                                               '/X/E'))) XT) LOOP

      EXIT WHEN I.SEROTESTIDS IS NULL;

      V_ISSEROTESTSID    := TO_NUMBER(SUBSTR(I.SEROTESTIDS,
                                             1,
                                             INSTR(I.SEROTESTIDS, '-') - 1));
      V_ISSEROTESTVALUES := SUBSTR(I.SEROTESTIDS,
                                   INSTR(I.SEROTESTIDS, '-') + 1);

      DBMS_OUTPUT.PUT_LINE(V_ISSEROTESTSID || ',' || V_ISSEROTESTVALUES);

      SELECT COUNT(*)
        INTO V_COUNT
        FROM LAB.RAISEREQUEST RR
        LEFT OUTER JOIN LAB.REQUESTTESTS RT
          ON RR.LRN = RT.LRN
        LEFT OUTER JOIN LAB.LABREPORTS LR
          ON LR.REQUESTTESTID = RT.REQUESTTESTID
        LEFT OUTER JOIN LAB.TESTREPORTS TR
          ON TR.LABREPORTID = LR.LABREPORTID
       WHERE RR.PATIENTSERVICE = 'BLOODBANK'
         AND RR.LRN = V_LRN
         AND RT.TESTID = V_ISSEROTESTSID
         AND UPPER(TR.RESULT) = V_ISSEROTESTVALUES
         AND RR.UHID = IV_UHID
         AND RR.LOCATIONID = IV_LOCATION;

      DBMS_OUTPUT.PUT_LINE(V_COUNT);

    END LOOP;

    IF V_COUNT > 0 THEN

      OV_RESULT := 'Y';

    ELSE
      OV_RESULT := 'N';

    END IF;

  END IF;

END;

"""

object_converted_output = """
set search_path to AICCTEST;
create or replace procedure AICCTEST.P_GETISSEROPOSITIVE(IV_UHID IN varchar,IV_LOCATION IN varchar,OV_RESULT inout CHARACTER)language plpgsql
security definer as $BODY$
declare
V_BLOODNUMBER varchar(4000);
V_LRN numeric;
V_ISSEROTESTSID numeric;
V_COUNT numeric;
V_ISSEROTESTVALUES varchar(4000);
I record;
BEGIN
set search_path to AICCTEST;
begin
SELECT Coalesce(BBAG.BLOODBAGNUMBER,DD.TOKENNO)into strict V_BLOODNUMBER
FROM BB.DONORMASTER DM
LEFT OUTER JOIN BB.DONORDETAILS DD
ON DD.DONORID = DM.DONORID
LEFT OUTER JOIN BB.BLOODBAG BBAG
ON DD.VISITID = BBAG.VISITID
WHERE DM.UHID = IV_UHID
AND DD.LOCATIONID = IV_LOCATION
limit 1
--AND UPPER(DD.TOKENSTATUS) LIKE 'CLOSED'


ORDER BY DM.CREATEDDATE DESC;
EXCEPTION
WHEN NO_DATA_FOUND THEN
V_BLOODNUMBER :=null;
END;
IF V_BLOODNUMBER IS NULL THEN
OV_RESULT := 'N';
ELSE
SELECT RR.LRN
into strict V_LRN
FROM LAB.RAISEREQUEST RR
WHERE RR.PATIENTSERVICENO = V_BLOODNUMBER
AND RR.PATIENTSERVICE = 'BLOODBANK'
AND RR.LOCATIONID = IV_LOCATION
ORDER BY RR.CREATEDDATE DESC limit 1;
FOR I IN(SELECT(case when(select unnest(xpath('E',XT.COLUMN_VALUE)))::text='' then null
else(select unnest(xpath('E',XT.COLUMN_VALUE)))::text end)AS SEROTESTIDS
FROM TABLE(XMLSEQUENCE(extract(xml(REPLACE(('<X><E>' ||(SELECT BCNF.CONFIGVALUE
FROM BB.BBCONFIG BCNF
WHERE UPPER(BCNF.CONFIG_KEY)=
'ISSEROPOSITIVETESTS')||
'</E></X>'),',','</E><E>')),'/X/E')))XT)LOOP
EXIT WHEN I.SEROTESTIDS IS NULL;
V_ISSEROTESTSID := TO_NUMBER(SUBSTR(I.SEROTESTIDS,1,public.instr(I.SEROTESTIDS,'-')- 1));
V_ISSEROTESTVALUES := SUBSTR(I.SEROTESTIDS,public.instr(I.SEROTESTIDS,'-')+ 1);
Raise Notice '% ,%',V_ISSEROTESTSID,V_ISSEROTESTVALUES;
SELECT COUNT(*)into strict V_COUNT
FROM LAB.RAISEREQUEST RR
LEFT OUTER JOIN LAB.REQUESTTESTS RT
ON RR.LRN = RT.LRN
LEFT OUTER JOIN LAB.LABREPORTS LR
ON LR.REQUESTTESTID = RT.REQUESTTESTID
LEFT OUTER JOIN LAB.TESTREPORTS TR
ON TR.LABREPORTID = LR.LABREPORTID
WHERE RR.PATIENTSERVICE = 'BLOODBANK'
AND RR.LRN = V_LRN
AND RT.TESTID = V_ISSEROTESTSID
AND UPPER(TR.RESULT)= V_ISSEROTESTVALUES
AND RR.UHID = IV_UHID
AND RR.LOCATIONID = IV_LOCATION;
Raise Notice '%',V_COUNT;
END LOOP;
IF V_COUNT > 0 THEN
OV_RESULT := 'Y';
ELSE
OV_RESULT := 'N';
END IF;
END IF;
end;
$BODY$
;
"""

deploy_error = """syntax error at or near "ORDER"
LINE 26: ORDER BY DM.CREATEDDATE DESC;
         ^"""
tgt_id = 194418
dr_connection_id = 7
target_connection_id = None
target_schema = "AICCTEST"
object_type = "Procedure"
objectname = "P_GETISSEROPOSITIVE"
iteration_id = 521
cloud_category = "Cloud"
migration_name = 'Oracle_Postgres14'
project_id = 1235
max_attempt_per_statement = 5

ai_connection_url = 'https://dvnext.qmigrator.ai/ai/conversion/conversion-agent'
headers = {
    'Authorization': 'Bearer ' + 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************.V-JpgCaEy0JE5Cr9M8nBC5wGLwsFsM9WVGmCPxskfeI'
}

payload = {
    "project_id": project_id,
    "migration_name": migration_name,
    "source_code": original_source_code,
    "target_code": object_converted_output,
    "deployment_error": str(deploy_error),
    "max_attempt_per_statement": max_attempt_per_statement,
    "tgt_object_id": tgt_id,
    "dr_connection_id": dr_connection_id,
    "target_connection_id": target_connection_id,
    "target_schema_name": target_schema,
    "object_type": object_type,
    "objectname": objectname,
    "run_number": iteration_id,
    "cloud_category": cloud_category
}

response = requests.post(ai_connection_url, data=payload, headers=headers)
status_code = response.status_code
print(status_code)
if status_code == 200:
    print(f"{migration_name} Conversion Agent Stage 1 API successfully Triggered")
else:
    raise Exception(
        f"{migration_name}: Request Failed while calling Conversion Agent Stage 1 API")