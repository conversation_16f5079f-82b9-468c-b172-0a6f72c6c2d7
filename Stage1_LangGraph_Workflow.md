# Stage 1 LangGraph Workflow Documentation

## Overview
Stage 1 LangGraph workflow processes database migration errors through a comprehensive 10-step AI-driven analysis and correction process. The workflow handles statement splitting, error identification, source mapping, conversion, validation, and deployment with intelligent retry mechanisms and iterative improvement loops.

## Workflow Diagram

```mermaid
flowchart TD
    A[Start Stage1 Workflow] --> B[Split Statements]

    B --> C[Analyze Error & Identify Target Statements]

    C --> D{Validate Error Identification}
    D -->|Failed| C
    D -->|Success| E[Map Source with Target Statements]

    E --> F{Validate Source Mapping}
    F -->|Failed| E
    F -->|Target-Specific| G[Convert Target Statement]
    F -->|Success| G

    G --> H{Validate Conversion}
    H -->|Failed| G
    H -->|Success| I[Replace Target Statement]

    I --> J[Target Code Deployment]

    J --> K{Deployment Status Check}
    K -->|Failed & Max Iterations| L[End - Max Attempts Reached]
    K -->|Failed & Retry| C
    K -->|Success| M[End - Success]

    %% Styling
    classDef startEnd fill:#e1f5fe,stroke:#01579b,stroke-width:2px,color:#000
    classDef decision fill:#fff3e0,stroke:#e65100,stroke-width:2px,color:#000
    classDef processing fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px,color:#000
    classDef validation fill:#fff8e1,stroke:#f57f17,stroke-width:2px,color:#000
    classDef deployment fill:#fce4ec,stroke:#c2185b,stroke-width:2px,color:#000

    class A,L,M startEnd
    class D,F,H,K decision
    class B,C,E,G,I processing
    class J deployment
```

## Workflow Steps

### 1. Start Stage1 Workflow
- **Entry Point**: Initialize Stage 1 LangGraph workflow
- **Purpose**: Set up workflow context and prepare for statement processing
- **Actions**:
  - Initialize workflow state with source code, target code, and deployment error
  - Set up logging and monitoring
  - Prepare workflow context for subsequent processing
  - Initialize iteration count and attempt history

### 2. Split Statements
- **Node**: `splitStatements`
- **Purpose**: Parse SQL code into individual statements for granular processing
- **Process**:
  - Split source code into individual SQL statements
  - Split target code using AdvancedPositionMapper for comprehensive mapping
  - Preserve original target statements for reference throughout workflow
  - Initialize attempt history tracking for each statement
  - Save original target code to conversion path for backup

#### Database Operations:
```sql
-- Insert original target statements into tgt_statements table
INSERT INTO public.tgt_statements (
    tgt_id, statement_number, target_statement, 
    converted_statement, source_statement, created_dt
) VALUES (?, ?, ?, NULL, NULL, NOW())
```

#### Output Format:
```
{
    "source_statements": List of individual source SQL statements,
    "target_statements": List of individual target SQL statements,
    "original_target_statements": Preserved original target statements,
    "attempt_history": Dictionary tracking previous attempts per statement,
    "target_position_mapper": Position mapping data for error resolution,
    "iteration_count": Current workflow iteration number
}
```

### 3. Analyze Error & Identify Target Statements
- **Node**: `AnalyzeError_identifyTargetStatements`
- **Purpose**: Identify problematic target statement using hybrid position-based and AI analysis
- **Process**:
  - Use SmartStatementResolver for position-based error identification
  - Fallback to AI-based identification if position-based fails
  - Extract error context (before/error/after statements)
  - Handle edge cases like target-specific statements

#### Position-Based Resolution:
- Parse deployment error for line numbers and character positions
- Map error positions to specific target statements
- Extract surrounding context for comprehensive analysis

#### AI Fallback Process:
- Two-phase AI identification when position-based fails
- Phase 1: Identify potential error statements
- Phase 2: Validate and select final error statement
- Consider previous feedback for iterative improvement

#### Output Format:
```
{
    "target_error_context": {
        error_statement_number: Sequential number of the problematic statement,
        error_statement: The actual SQL statement causing the error,
        before_statements: List of statements preceding the error,
        after_statements: List of statements following the error,
        context_range: Range of statement numbers for context analysis
    }
}
```

### 4. Validate Error Identification
- **Node**: `validate_error_identification`
- **Purpose**: AI validates the identified error statement for accuracy
- **Validation Criteria**:
  - Verify error statement matches deployment error
  - Check if error context is sufficient for analysis
  - Validate error statement syntax and semantics
  - Ensure error identification is not a false positive

#### Retry Logic:
- **Success**: Proceed to source mapping
- **Failure**: Return to error identification with feedback
- **Feedback Loop**: Provide validation results to improve next attempt

### 5. Map Source with Target Statements  
- **Node**: `mapSource_withTargetStatements`
- **Purpose**: Map target error statements to corresponding source statements
- **Process**:
  - Use AI-driven intelligent mapping between source and target
  - Handle edge cases like target-specific statements
  - Consider statement context and functional equivalence
  - Generate comprehensive mapping with confidence scores

#### Mapping Strategies:
1. **Direct Mapping**: One-to-one statement correspondence
2. **Contextual Mapping**: Consider surrounding statements for context
3. **Functional Mapping**: Map based on functional equivalence
4. **Edge Case Handling**: Handle target-specific or source-only statements

#### Output Format:
```
{
    "source_context": {
        error_statement_number: Statement number in source (0 for target-specific),
        error_statement: Corresponding source statement,
        before_statements: Source statements providing context,
        after_statements: Source statements following the mapped statement,
        mapping_confidence: AI confidence score for the mapping accuracy
    }
}
```

### 6. Validate Source Mapping
- **Node**: `validate_source_mapping`
- **Purpose**: Validate the accuracy of source-to-target statement mapping
- **Validation Process**:
  - AI validates mapping accuracy and relevance
  - Check functional equivalence between mapped statements
  - Verify mapping context is sufficient for conversion
  - Handle optimization for target-specific statements

#### Routing Logic:
- **Success**: Proceed to statement conversion
- **Failure**: Return to source mapping with feedback
- **Target-Specific Optimization**: Skip validation for target-only statements
- **Feedback Integration**: Use validation results to improve mapping

### 7. Convert Target Statement
- **Node**: `Convert_TargetStatement`
- **Purpose**: AI-driven SQL conversion with comprehensive error correction
- **Process**:
  - Analyze source and target context for conversion requirements
  - Generate corrected SQL statements with detailed change tracking
  - Provide functional validation and explanation of changes
  - Handle complex SQL conversion scenarios with database-specific optimizations

#### AI Conversion Features:
- **Context-Aware**: Consider surrounding statements and database context
- **Change Tracking**: Detailed documentation of all modifications
- **Functional Validation**: Ensure converted statements maintain functionality
- **Database-Specific**: Apply target database-specific optimizations
- **Error Handling**: Address specific deployment errors with targeted fixes

#### Output Format:
```
{
    "ai_corrections": List of corrected statements with change tracking,
    "conversion_explanation": Detailed explanation of conversion decisions,
    "functional_validation": {
        is_functionally_equivalent: Boolean indicating functional equivalence,
        validation_details: Detailed validation analysis,
        potential_issues: List of identified potential problems
    }
}
```

### 8. Validate Conversion
- **Node**: `validate_conversion`
- **Purpose**: Validate AI-generated corrections for quality and accuracy
- **Validation Criteria**:
  - Syntax validation for target database compatibility
  - Functional equivalence verification
  - Performance impact assessment
  - Security and best practices compliance

#### Validation Components:
- **Syntax Validation**: Check SQL syntax for target database
- **Functional Validation**: Verify logical equivalence with source
- **Performance Validation**: Assess potential performance impacts
- **Security Validation**: Check for security vulnerabilities

#### Retry Logic:
- **Success**: Proceed to statement replacement
- **Failure**: Return to conversion with detailed feedback
- **Iterative Improvement**: Use validation feedback for better conversions

### 9. Replace Target Statement
- **Node**: `replaceTargetStatement`
- **Purpose**: Prepare corrected code for deployment by replacing error statements
- **Process**:
  - Replace identified error statements with AI corrections
  - Maintain statement numbering and structure
  - Preserve non-error statements unchanged
  - Generate deployment-ready target code

#### Replacement Strategy:
- **Targeted Replacement**: Only replace identified error statements
- **Structure Preservation**: Maintain original code structure and formatting
- **Version Tracking**: Track all statement modifications for audit trail
- **Rollback Capability**: Preserve original statements for potential rollback

### 10. Target Code Deployment
- **Node**: `targetcode_deployment`
- **Purpose**: Deploy corrected code to target database and capture results
- **Process**:
  - Execute corrected SQL code on target database
  - Capture deployment results and any new errors
  - Log deployment attempt with detailed error information
  - Update attempt history for iterative improvement

#### Database Operations:
```sql
-- Update deployment attempt in conversion_agent_deployment table
INSERT INTO public.conversion_agent_deployment (
    tgt_statements_id, attempt, source_statement, target_statement,
    converted_statement, observations, is_deployed, error, created_dt
) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
```

#### Deployment Process:
- **Connection Management**: Establish secure connection to target database using dynamic connection logic
- **Transaction Handling**: Use transactions for safe deployment
- **Error Capture**: Comprehensive error logging and analysis
- **Result Tracking**: Track success/failure with detailed metrics

#### Connection ID Logic:
The Stage 1 conversion process implements intelligent connection routing based on object types:

**Default Behavior (All Objects):**
- Primary deployment uses **DR Connection ID** for all database objects
- Ensures consistent deployment to disaster recovery environment
- Provides fallback and testing capabilities before production deployment

**Special Handling (Table/Partition Objects):**
- Table and partition objects require **dual deployment strategy**
- First deploys to DR connection (same as all objects)
- Then performs additional deployment to **Target Connection ID** (if available)
- Ensures data objects are available in both DR and production environments

**Implementation Logic:**
```python
# Step 1: All objects deploy to DR connection
deployment_successful = deployToTargetDatabase(code, dr_db_credentials)

# Step 2: Table/partition objects also deploy to target connection
if deployment_successful and object_type in ['table', 'partition']:
    if target_db_credentials_available:
        deployToTargetDatabase(code, target_db_credentials)
```

**Business Rationale:**
- **DR-First Strategy**: Ensures all objects are tested in DR environment first
- **Data Object Availability**: Tables and partitions need to be accessible in production
- **Risk Mitigation**: Procedures/functions can run from DR, but data must be in target
- **Graceful Fallback**: If target connection unavailable, DR deployment still succeeds

### 11. Deployment Status Check
- **Node**: `deployment_status`
- **Purpose**: Determine workflow continuation based on deployment results
- **Decision Logic**:
  - **Success**: End workflow with success status
  - **Failure + Max Iterations**: End workflow with max attempts reached
  - **Failure + Retry Available**: Continue to next iteration

#### Iteration Management:
- **Max Iteration Check**: Prevent infinite loops with configurable limits
- **Attempt History**: Track all previous attempts for learning
- **Feedback Integration**: Use deployment results to improve next iteration
- **Success Criteria**: Define clear success conditions for workflow completion

## Database Tables

### tgt_statements Table:
```sql
CREATE TABLE public.tgt_statements (
    id BIGSERIAL PRIMARY KEY,
    tgt_id BIGINT NOT NULL,
    statement_number BIGINT NOT NULL,
    target_statement TEXT NULL,
    converted_statement TEXT NULL,
    source_statement TEXT NULL,
    created_dt TIMESTAMP DEFAULT NOW() NULL,
    updated_dt TIMESTAMP NULL,
    CONSTRAINT tgt_statements_unique UNIQUE (tgt_id, statement_number)
);
```

### conversion_agent_deployment Table:
```sql
CREATE TABLE public.conversion_agent_deployment (
    deployment_id BIGSERIAL PRIMARY KEY,
    tgt_statements_id BIGINT NOT NULL,
    attempt BIGINT NOT NULL,
    source_statement TEXT NULL,
    target_statement TEXT NULL,
    converted_statement TEXT NULL,
    observations TEXT NULL,
    is_deployed BOOLEAN DEFAULT FALSE NULL,
    error TEXT NULL,
    new_deployment_error TEXT NULL,  -- Enhanced: Current attempt deployment error
    updated_statement TEXT NULL,     -- Enhanced: Manual user edits only
    review_status BOOLEAN DEFAULT FALSE NULL,
    reviewer_comments TEXT NULL,
    reviewer_name TEXT NULL,
    created_dt TIMESTAMP DEFAULT NOW() NULL,
    updated_dt TIMESTAMP NULL,
    CONSTRAINT deployment_unique UNIQUE (tgt_statements_id, attempt),
    CONSTRAINT fk_tgt_statements FOREIGN KEY (tgt_statements_id)
        REFERENCES public.tgt_statements(id) ON DELETE CASCADE
);
```

## Enhanced Deployment Tracking

### Overview
The enhanced deployment tracking system provides statement-level error differentiation and complete audit trails for deployment attempts. This system supports both AI-driven conversions and manual user corrections through a unified approach.

### Key Features
- **Statement-Level Error Tracking**: Differentiate between original errors and attempt-specific errors
- **Complete Audit Trail**: Track every deployment attempt with specific error details
- **Manual User Edit Support**: Support for user corrections via `updated_statement` field
- **Automatic Fallback Logic**: COALESCE logic provides best available data to Stage2
- **Zero Stage2 Impact**: No changes needed to Stage2 tables or code

### Enhanced Database Schema

#### New Columns Added:
```sql
-- Add enhanced tracking columns to existing table
ALTER TABLE public.conversion_agent_deployment
ADD COLUMN IF NOT EXISTS new_deployment_error TEXT NULL,
ADD COLUMN IF NOT EXISTS updated_statement TEXT NULL;
```

#### Enhanced Stored Procedure:
```sql
CREATE OR REPLACE FUNCTION public.sp_conversion_agent_deployment_insert(
    IN p_tgt_id BIGINT,
    IN p_statement_number BIGINT,
    IN p_attempt BIGINT,
    IN p_source_statement TEXT,
    IN p_source_statement_number BIGINT,
    IN p_target_statement TEXT,
    IN p_converted_statement TEXT,
    IN p_observations TEXT,
    IN p_is_deployed BOOLEAN,
    IN p_error TEXT,
    IN p_new_deployment_error TEXT DEFAULT NULL,
    IN p_updated_statement TEXT DEFAULT NULL,
    INOUT ref REFCURSOR DEFAULT 'refcursor'
)
RETURNS REFCURSOR
LANGUAGE plpgsql
AS $$
BEGIN
    INSERT INTO public.conversion_agent_deployment (
        tgt_statements_id, attempt, source_statement, target_statement,
        converted_statement, observations, is_deployed, error,
        new_deployment_error, updated_statement, created_dt
    ) VALUES (
        (SELECT id FROM public.tgt_statements WHERE tgt_id = p_tgt_id AND statement_number = p_statement_number),
        p_attempt, p_source_statement, p_target_statement,
        p_converted_statement, p_observations, p_is_deployed, p_error,
        p_new_deployment_error, p_updated_statement, NOW()
    );

    OPEN ref FOR SELECT 'SUCCESS' as result;
    RETURN ref;
END;
$$;
```

### Error Field Logic

#### Two Types of Errors:

##### `error` Field:
- **Purpose**: Stores the "original" or "inherited" error for the current statement
- **Source**: Comes from previous statement's deployment error OR initial deployment error
- **Behavior**: Remains consistent across all attempts for the same statement
- **Usage**: Provides context about what led to processing this statement

##### `new_deployment_error` Field:
- **Purpose**: Stores the current attempt's specific deployment error
- **Source**: Generated from the current deployment attempt result
- **Behavior**: Changes with each failed attempt, NULL on success
- **Usage**: Tracks what specifically failed in this attempt

#### Statement Processing Flow:

##### Statement 1 Processing:
```python
# Attempt 1 for Statement 1
conversion_agent_deployment_insert(
    error="Initial deployment error from request",     # Original error
    new_deployment_error="Statement 1 attempt 1 error" or None,  # Current attempt result
    converted_statement="AI generated statement",
    updated_statement=None  # Not set by AI workflow
)

# Attempt 2 for Statement 1
conversion_agent_deployment_insert(
    error="Initial deployment error from request",     # SAME original error
    new_deployment_error="Statement 1 attempt 2 error" or None,  # NEW attempt result
    converted_statement="AI generated statement v2",
    updated_statement=None
)

# Attempt 3 for Statement 1 (Success)
conversion_agent_deployment_insert(
    error="Initial deployment error from request",     # SAME original error
    new_deployment_error=None,                         # NULL on success
    converted_statement="AI generated statement v3",
    updated_statement=None
)
```

##### Statement 2 Processing (Different Error):
```python
# Attempt 1 for Statement 2
conversion_agent_deployment_insert(
    error="Statement 1's final deployment error",      # Previous statement's error
    new_deployment_error="Statement 2 attempt 1 error" or None,  # Current attempt result
    converted_statement="AI generated statement",
    updated_statement=None
)

# Attempt 2 for Statement 2
conversion_agent_deployment_insert(
    error="Statement 1's final deployment error",      # SAME inherited error
    new_deployment_error="Statement 2 attempt 2 error" or None,  # NEW attempt result
    converted_statement="AI generated statement v2",
    updated_statement=None
)
```

### Updated Statement Logic

#### AI Workflow vs Manual User Edits:

##### `converted_statement` Field:
- **Set by**: AI workflow during conversion process
- **Purpose**: Stores AI-generated converted statements
- **Updates**: Changes with each AI attempt/correction

##### `updated_statement` Field:
- **Set by**: Manual user edits in database table only
- **Purpose**: Stores user corrections after reviewing AI converted statements
- **AI Workflow**: Always sets this to NULL (not used by AI)
- **Usage**: Allows users to manually improve AI conversions

#### COALESCE Fallback Logic:
```sql
-- Automatic fallback in database query for CSV generation
SELECT
    COALESCE(cad.updated_statement, cad.converted_statement) as ai_converted_statement,
    cad.error as deployment_error
FROM public.conversion_agent_deployment cad
WHERE cad.review_status = true AND cad.is_manually_deployed = true
```

**Logic**:
- **Statement Selection**: Use `updated_statement` if user edited, otherwise use `converted_statement`
- **Error Selection**: Use `error` field for original/inherited error context (not attempt-specific errors)

#### User Workflow:
1. **AI generates** `converted_statement`
2. **User reviews** and can manually edit `updated_statement` in database
3. **COALESCE automatically picks** the best available statement:
   - If user edited: uses `updated_statement`
   - If not edited: uses `converted_statement`

### Practical Example

#### Scenario: 3 Statements, Multiple Attempts

| Statement | Attempt | `error` (inherited) | `new_deployment_error` (current) | `converted_statement` | `updated_statement` | Result |
|-----------|---------|-------------------|--------------------------------|---------------------|-------------------|---------|
| 1 | 1 | "Initial error" | "Stmt1 Att1 error" | "AI conversion v1" | NULL | Failed |
| 1 | 2 | "Initial error" | NULL | "AI conversion v2" | NULL | Success |
| 2 | 1 | "Stmt1 Att1 error" | "Stmt2 Att1 error" | "AI conversion v1" | NULL | Failed |
| 2 | 2 | "Stmt1 Att1 error" | "Stmt2 Att2 error" | "AI conversion v2" | NULL | Failed |
| 2 | 3 | "Stmt1 Att1 error" | NULL | "AI conversion v3" | "User edit" | Success |
| 3 | 1 | "Stmt2 Att2 error" | NULL | "AI conversion v1" | NULL | Success |

#### CSV Generation Results (COALESCE):

| Statement | `ai_converted_statement` (COALESCE) | `deployment_error` | Source |
|-----------|-----------------------------------|-------------------|---------|
| 1 | "AI conversion v2" | "Initial error" | converted_statement, error |
| 2 | "User edit" | "Stmt1 Att1 error" | updated_statement, error |
| 3 | "AI conversion v1" | "Stmt2 Att2 error" | converted_statement, error |

### Implementation Benefits

#### 1. Complete Audit Trail
- Every deployment attempt is recorded with specific error details
- Can trace exactly what failed in each attempt
- Historical view of AI improvement across attempts

#### 2. Statement-Level Error Differentiation
- Clear separation between inherited errors and attempt-specific errors
- Can identify which statement caused which error
- Better debugging and analysis capabilities

#### 3. User Edit Support
- Users can manually improve AI conversions
- Automatic fallback ensures best available statement is used
- No workflow disruption when users make edits

#### 4. Backward Compatibility
- Existing Stage2 functionality unchanged
- Same CSV format with enhanced data
- Gradual adoption possible

#### 5. Error Inheritance Logic
- Errors flow naturally from statement to statement
- Maintains context about what led to processing each statement
- Supports complex multi-statement error scenarios

### Stage2 Integration

#### No Stage2 Changes Required:
- **Tables**: No changes to `stage2_statements` table
- **Code**: No changes to Stage2 processing logic
- **APIs**: No changes to Stage2 stored procedures

#### Automatic Enhancement:
Stage2 automatically receives enhanced data through existing CSV format:
- `ai_converted_statement`: Best available statement (user edit or AI conversion)
- `deployment_error`: Original/inherited error context (not attempt-specific errors)

This ensures Stage2 gets the proper context for processing while maintaining complete audit trails in Stage1.

## Workflow State Management

### WorkflowState Structure:
The workflow maintains comprehensive state information throughout the migration process:

#### Input Parameters:
- **source_code**: Original source database code
- **target_code**: Target database code with deployment errors
- **deployment_error**: Error message from target database deployment

#### Statement Processing:
- **source_statements**: Individual source SQL statements after splitting
- **target_statements**: Individual target SQL statements after splitting
- **original_target_statements**: Preserved original target statements for reference

#### Error Context:
- **target_error_context**: Context around the identified error statement
- **source_context**: Corresponding source statement context and mapping

#### AI Processing Results:
- **ai_corrections**: List of AI-generated statement corrections
- **conversion_explanation**: Detailed explanation of conversion decisions
- **functional_validation**: Validation results for converted statements

#### Workflow Control:
- **iteration_count**: Current workflow iteration number
- **max_attempt_per_statement**: Maximum attempts allowed per statement
- **attempt_history**: History of previous attempts for learning

#### Database and Configuration:
- **conversion_path**: File system path for conversion artifacts
- **target_db_credentials**: Target database connection credentials
- **project_db_credentials**: Project database connection credentials
- **migration_name**: Name of the migration (e.g., Oracle_Postgres14)
- **target_object_id**: Unique identifier for the target object
- **object_type**: Type of database object being migrated

#### Validation Tracking:
- **error_identification_successful**: Success status of error identification
- **error_identification_attempts**: Number of error identification attempts
- **error_identification_feedback**: Feedback from validation failures
- **source_mapping_successful**: Success status of source mapping
- **source_mapping_attempts**: Number of source mapping attempts
- **source_mapping_feedback**: Feedback from mapping validation
- **conversion_validation_successful**: Success status of conversion validation
- **conversion_validation_attempts**: Number of conversion validation attempts
- **conversion_validation_feedback**: Feedback from conversion validation

### Data Structures:

#### ErrorContext:
- **error_statement_number**: Sequential number of the problematic statement
- **error_statement**: The actual SQL statement causing the error
- **before_statements**: List of statements preceding the error
- **after_statements**: List of statements following the error
- **context_range**: Range of statement numbers for context analysis
- **mapping_confidence**: AI confidence score for mapping accuracy
- **error_type**: Classification of the error type
- **error_details**: Detailed error information and context

#### CorrectedStatement:
- **statement_number**: Sequential statement number in target code
- **original_statement**: Original statement before correction
- **corrected_statement**: AI-generated corrected statement
- **statement_type**: Context type (before_error, error_statement, after_error)
- **changes_made**: Detailed description of changes made
- **confidence_score**: AI confidence in the correction
- **error_addressed**: Specific error addressed by the correction
- **database_specific_notes**: Target database-specific considerations

#### FunctionalValidation:
- **is_functionally_equivalent**: Boolean indicating functional equivalence
- **validation_details**: Detailed validation analysis and reasoning
- **potential_issues**: List of identified potential problems
- **performance_impact**: Assessment of performance implications
- **security_considerations**: Security-related validation notes

## Conditional Routing Logic

### Error Identification Validation:
- **Success Route**: Proceed to source mapping when error identification is successful
- **Retry Route**: Return to error identification with feedback when validation fails
- **Termination Route**: End workflow when maximum error identification attempts reached
- **Feedback Integration**: Use validation feedback to improve subsequent identification attempts

### Source Mapping Validation:
- **Success Route**: Continue to conversion when source mapping is validated successfully
- **Retry Route**: Return to source mapping with feedback when validation fails
- **Optimization Route**: Skip validation for target-specific statements (error_statement_number = 0)
- **Fallback Route**: Continue to conversion even if mapping fails after maximum attempts
- **Target-Specific Handling**: Automatically route target-only statements directly to conversion

### Conversion Validation:
- **Success Route**: Proceed to statement replacement when conversion validation passes
- **Retry Route**: Return to conversion with detailed feedback when validation fails
- **Quality Assurance**: Ensure converted statements meet syntax and functional requirements
- **Fallback Route**: Continue with best available conversion after maximum attempts
- **Iterative Improvement**: Use validation feedback to enhance conversion quality

### Deployment Status Check:
- **Success Route**: End workflow when deployment completes without errors
- **Retry Route**: Return to error analysis for new iteration when deployment fails
- **Termination Route**: End workflow when maximum iteration count reached
- **Iteration Management**: Increment iteration count and reset validation states for retry
- **Learning Integration**: Use deployment results to improve subsequent iterations

## API Integration

### Connection ID Parameters:
The Stage 1 API accepts both DR and target connection IDs to support the dual deployment strategy:

**Required Parameters:**
- `dr_connection_id` (int): DR database connection ID - used for all object deployments
- `object_type` (str): Type of database object (procedure, function, table, partition, view, etc.)

**Optional Parameters:**
- `target_connection_id` (int, optional): Target database connection ID - used for table/partition objects

**Parameter Usage:**
```python
# API Request Example
{
    "dr_connection_id": 123,           # Required - used for all objects
    "target_connection_id": 456,       # Optional - used for table/partition only
    "object_type": "table",            # Determines deployment strategy
    "migration_name": "Oracle_Postgres14",
    "source_code": "...",
    "target_code": "...",
    "deployment_error": "..."
}
```

**Setup Process:**
1. **DR Credentials**: Always retrieved using `dr_connection_id` from API
2. **Target Credentials**: Retrieved using `target_connection_id` (if provided)
3. **Credential Validation**: Both credentials validated before workflow execution
4. **Fallback Handling**: Workflow continues with DR-only deployment if target credentials unavailable

### Stage 1 Endpoint:
- **Endpoint**: `/conversion-agent` (POST)
- **Purpose**: Convert database code using AI-driven migration workflow
- **Execution Model**: Background task processing for long-running workflows
- **Response**: Immediate acknowledgment with background processing status

#### Input Parameters:
- **source_code**: Original source database code for analysis
- **target_code**: Target database code with deployment errors
- **deployment_error**: Error message from target database deployment attempt
- **max_attempt_per_statement**: Maximum attempts per target statement (default: 5)
- **tgt_object_id**: Target object ID for the migration
- **dr_connection_id**: DR database connection ID (required for all objects)
- **target_connection_id**: Target database connection ID (optional, used for table/partition)
- **migration_name**: Name of the migration (e.g., Oracle_Postgres14)
- **project_id**: Project ID for the migration
- **objectname**: Name of the object being migrated
- **cloud_category**: Cloud category for the migration
- **run_number**: Run number for the migration
- **target_schema_name**: Target schema name
- **object_type**: Type of the object being migrated (determines deployment strategy)

### Workflow Execution Process:
#### Environment Setup:
- **Pre-Setup Manager**: Initialize migration environment and credentials
- **Database Credentials**: Fetch DR and target database credentials from API using connection IDs
- **Credential Management**: Store both DR and target credentials in workflow state
- **Conversion Path**: Set up file system paths for conversion artifacts
- **LLM Initialization**: Configure and initialize AI language model

#### Credential Setup Logic:
```python
# DR credentials (always required)
dr_db_credentials = decrypt_database_details(
    token_data, project_id, 'Target', str(dr_connection_id)
)

# Target credentials (optional, for table/partition objects)
if target_connection_id is not None:
    target_db_credentials = decrypt_database_details(
        token_data, project_id, 'Target', str(target_connection_id)
    )
```

#### Workflow State Initialization:
- **dr_db_credentials**: DR database connection details (always present)
- **target_db_credentials**: Target database connection details (optional)
- **object_type**: Database object type for deployment routing
- **migration_name**: Dynamic database connection configuration

#### Graph Builder Setup:
- **Graph Construction**: Build LangGraph workflow with all nodes and edges
- **Memory Configuration**: Set up workflow state persistence and checkpointing
- **Visualization**: Generate workflow diagram for debugging and documentation

#### Workflow Invocation:
- **Initial State**: Configure workflow with all input parameters and credentials
- **Thread Management**: Create unique thread ID for workflow execution tracking
- **Background Execution**: Run complete migration workflow in background task
- **Status Tracking**: Monitor workflow progress and update database status

#### Deployment Flow Decision Logic:
The workflow implements intelligent deployment routing based on object type and credential availability:

**Decision Matrix:**
| Object Type | DR Deployment | Target Deployment | Notes |
|-------------|---------------|-------------------|-------|
| Procedure   | ✅ Always     | ❌ Never         | Code objects run from DR |
| Function    | ✅ Always     | ❌ Never         | Code objects run from DR |
| View        | ✅ Always     | ❌ Never         | Code objects run from DR |
| Trigger     | ✅ Always     | ❌ Never         | Code objects run from DR |
| Table       | ✅ Always     | ✅ If Available  | Data objects need target access |
| Partition   | ✅ Always     | ✅ If Available  | Data objects need target access |

**Deployment Sequence:**
1. **Primary Deployment**: All objects deploy to DR connection first
2. **Validation**: Check deployment success before proceeding
3. **Secondary Deployment**: Table/partition objects deploy to target (if credentials available)
4. **Error Handling**: Target deployment failure doesn't affect DR deployment success
5. **Status Tracking**: Both deployments tracked separately in database

## Key Features

### Hybrid Error Identification:
- **Position-Based Resolution**: Fast and accurate error identification using SmartStatementResolver
  - Parses deployment errors for line numbers and character positions
  - Maps error positions to specific target statements with high precision
  - Handles standard PostgreSQL, Oracle, MySQL, and SQL Server error formats
- **AI-Fallback System**: Handles complex or non-standard error messages
  - Two-phase AI identification process for comprehensive analysis
  - Context-aware error detection with surrounding statement analysis
  - Learning from previous feedback to improve accuracy
- **Edge Case Handling**: Manages target-specific statements and complex scenarios
  - Detects statements that exist only in target database
  - Handles multi-statement errors and cascading failures
  - Supports complex SQL constructs and database-specific syntax

### Intelligent Source Mapping:
- **Context-Aware Mapping**: Considers surrounding statements for accurate correspondence
  - Analyzes statement context and dependencies
  - Maps based on functional relationships, not just syntactic similarity
  - Handles statement reordering and structural changes
- **Functional Equivalence**: Maps based on logical functionality rather than syntax
  - Identifies semantically equivalent statements across different SQL dialects
  - Handles database-specific function mappings and syntax variations
  - Maintains business logic integrity during mapping
- **Optimization Features**: Skips unnecessary mapping for target-specific statements
  - Automatically detects target-only statements (error_statement_number = 0)
  - Bypasses source mapping validation for efficiency
  - Reduces processing time while maintaining accuracy

### AI-Driven Conversion:
- **Database-Specific Optimizations**: Applies target database-specific improvements
  - Leverages database-specific function libraries and syntax rules
  - Optimizes for target database performance characteristics
  - Handles vendor-specific SQL extensions and limitations
- **Context Integration**: Uses full statement context for accurate conversion
  - Analyzes surrounding statements for dependency resolution
  - Maintains referential integrity and constraint relationships
  - Preserves transaction boundaries and isolation levels
- **Change Tracking**: Detailed documentation of all modifications
  - Records every change made during conversion process
  - Provides explanations for each modification decision
  - Maintains audit trail for compliance and debugging
- **Validation Integration**: Built-in functional and syntax validation
  - Real-time syntax checking for target database compatibility
  - Functional equivalence verification between source and target
  - Performance impact assessment and optimization suggestions

### Iterative Improvement:
- **Feedback Loops**: Each validation failure provides feedback for improvement
  - Captures detailed failure reasons and context
  - Uses feedback to refine subsequent conversion attempts
  - Learns from patterns in successful and failed conversions
- **Attempt History**: Tracks all previous attempts to avoid repeated mistakes
  - Maintains history of last 10 attempts per statement
  - Prevents infinite loops and repeated failed approaches
  - Enables learning from previous iteration patterns
- **Learning Integration**: Uses previous failures to improve subsequent attempts
  - Analyzes failure patterns to identify common issues
  - Adapts conversion strategies based on historical success rates
  - Improves accuracy over time through accumulated experience
- **Configurable Limits**: Prevents infinite loops with configurable iteration limits
  - Default maximum of 5 attempts per statement (configurable)
  - Graceful degradation when limits are reached
  - Clear termination criteria and status reporting

### Comprehensive Audit Trail:
- **Database Persistence**: All attempts and results stored in database tables
  - Complete history in `tgt_statements` and `conversion_agent_deployment` tables
  - Timestamped records for chronological analysis
  - Foreign key relationships for data integrity
- **Version Tracking**: Complete history of all statement modifications
  - Original, intermediate, and final versions preserved
  - Change attribution and reasoning documentation
  - Rollback capability for failed deployments
- **Error Logging**: Detailed error capture and analysis
  - Comprehensive error context and stack traces
  - Error categorization and pattern analysis
  - Integration with monitoring and alerting systems
- **Performance Metrics**: Track conversion success rates and performance
  - Success rate tracking by database type and migration pattern
  - Performance benchmarking and optimization opportunities
  - Resource utilization monitoring and capacity planning

## Integration with Stage 2

### Data Flow to Stage 2:
- **Approved Statements**: Stage 1 successful conversions become input for Stage 2
- **Feature Identification**: Stage 2 analyzes which QMigrator features were used
- **Module Updates**: Stage 2 updates Python modules based on Stage 1 corrections
- **Continuous Improvement**: Stage 2 feedback improves Stage 1 conversion patterns

### Workflow Coordination:
- **Sequential Processing**: Stage 2 processes Stage 1 approved statements
- **Shared Database**: Both stages use common database for state management
- **Migration Context**: Shared migration_name and target_object_id for coordination
- **Quality Assurance**: Stage 2 validates and improves Stage 1 conversion quality

This comprehensive Stage 1 workflow provides robust database migration capabilities with intelligent AI-driven analysis, comprehensive error handling, iterative improvement mechanisms, and seamless integration with Stage 2 for optimal conversion results and continuous system improvement.
